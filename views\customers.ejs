<%- include('layout', { title: 'Customers', currentRoute: '/customers' }) %>
<% layout('layout') -%>
<main class="content-area">
<!-- Customer Management page content -->
<div class="container-fluid py-3">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-users me-2 text-primary"></i>Customer Management
        </h2>
        <div class="d-flex gap-2">
            <a href="/signup" class="btn btn-success btn-sm" target="_blank">
                <i class="fas fa-user-plus me-1"></i>Promotional Signup
            </a>
            <button class="btn btn-outline-primary btn-sm" id="exportCustomers">
                <i class="fas fa-download me-1"></i>Export Data
            </button>
        </div>
    </div>

<% if (typeof error !== 'undefined' && error) { %>
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i><%= error %>
    </div>
<% } %>

<!-- Customer Analytics Summary -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Customers</h6>
                        <h3 class="mb-0"><%= customers ? customers.length : 0 %></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Order Customers</h6>
                        <h3 class="mb-0"><%= customers ? customers.filter(c => !c.isPromoCustomer).length : 0 %></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Promo Signups</h6>
                        <h3 class="mb-0"><%= customers ? customers.filter(c => c.isPromoCustomer).length : 0 %></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-gift fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Revenue</h6>
                        <h3 class="mb-0">$<%= customers ? customers.reduce((sum, c) => sum + (c.totalSpent || 0), 0).toFixed(2) : '0.00' %></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-search me-2"></i>Search & Filter Customers
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <input type="text" id="searchInput" class="form-control" placeholder="Search by name, email, or phone...">
            </div>
            <div class="col-md-3">
                <select id="sourceFilter" class="form-select">
                    <option value="">All Sources</option>
                    <option value="order">Order Customers</option>
                    <option value="promotional_signup">Promotional Signups</option>
                </select>
            </div>
            <div class="col-md-3">
                <select id="marketingFilter" class="form-select">
                    <option value="">All Marketing Prefs</option>
                    <option value="email">Email Marketing</option>
                    <option value="sms">SMS Marketing</option>
                    <option value="both">Both Email & SMS</option>
                </select>
            </div>
        </div>
    </div>
</div>

<div class="card table-container">
    <div class="card-header">
        <h2 class="card-title">All Customers</h2>
    </div>
    <table class="table">
        <thead>
            <tr>
                <th>Customer</th>
                <th>Contact Info</th>
                <th>Source & Marketing</th>
                <th>Order History</th>
                <th>Total Spent</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="customersTableBody">
            <% if (!Array.isArray(customers) || customers.length === 0) { %>
                <tr>
                    <td colspan="6" class="text-center py-4">
                        <i class="fas fa-users fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No customer data available. Customer profiles will appear here once orders are processed or promotional signups are submitted.</p>
                    </td>
                </tr>
            <% } else { %>
                <% customers.forEach(customer => { %>
                    <tr data-customer-id="<%= customer.id %>"
                        data-source="<%= customer.source || (customer.isPromoCustomer ? 'promotional_signup' : 'order') %>"
                        data-email-marketing="<%= customer.marketingPreferences && customer.marketingPreferences.email ? 'true' : 'false' %>"
                        data-sms-marketing="<%= customer.marketingPreferences && customer.marketingPreferences.sms ? 'true' : 'false' %>">
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <% if (customer.isPromoCustomer) { %>
                                        <i class="fas fa-gift text-warning" title="Promotional Customer"></i>
                                    <% } else { %>
                                        <i class="fas fa-shopping-cart text-success" title="Order Customer"></i>
                                    <% } %>
                                </div>
                                <div>
                                    <strong><%= customer.name || 'Unknown Customer' %></strong>
                                    <% if (customer.id) { %>
                                        <br><small class="text-muted">ID: <%= customer.id.length > 15 ? customer.id.substring(0, 15) + '...' : customer.id %></small>
                                    <% } %>
                                    <% if (customer.joinedAt || customer.createdAt) { %>
                                        <br><small class="text-muted">Joined: <%= new Date(customer.joinedAt || customer.createdAt).toLocaleDateString() %></small>
                                    <% } %>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <% if (customer.email) { %>
                                    <div class="mb-1">
                                        <i class="fas fa-envelope me-1 text-primary"></i>
                                        <span><%= customer.email %></span>
                                    </div>
                                <% } %>
                                <% if (customer.phone) { %>
                                    <div class="mb-1">
                                        <i class="fas fa-phone me-1 text-success"></i>
                                        <span><%= customer.phone %></span>
                                    </div>
                                <% } %>
                                <% if (customer.address) { %>
                                    <div>
                                        <i class="fas fa-map-marker-alt me-1 text-info"></i>
                                        <small class="text-muted"><%= customer.address.length > 30 ? customer.address.substring(0, 30) + '...' : customer.address %></small>
                                    </div>
                                <% } %>
                                <% if (!customer.email && !customer.phone) { %>
                                    <small class="text-muted">No contact info</small>
                                <% } %>
                            </div>
                        </td>
                        <td>
                            <div>
                                <!-- Source Badge -->
                                <% if (customer.isPromoCustomer || customer.source === 'promotional_signup') { %>
                                    <span class="badge bg-warning text-dark mb-1">
                                        <i class="fas fa-gift me-1"></i>Promotional
                                    </span>
                                <% } else { %>
                                    <span class="badge bg-success mb-1">
                                        <i class="fas fa-shopping-cart me-1"></i>Order
                                    </span>
                                <% } %>

                                <!-- Marketing Preferences -->
                                <div class="mt-1">
                                    <% if (customer.marketingPreferences) { %>
                                        <% if (customer.marketingPreferences.email) { %>
                                            <small class="badge bg-primary me-1">
                                                <i class="fas fa-envelope"></i> Email
                                            </small>
                                        <% } %>
                                        <% if (customer.marketingPreferences.sms) { %>
                                            <small class="badge bg-info">
                                                <i class="fas fa-sms"></i> SMS
                                            </small>
                                        <% } %>
                                        <% if (!customer.marketingPreferences.email && !customer.marketingPreferences.sms) { %>
                                            <small class="text-muted">No marketing</small>
                                        <% } %>
                                    <% } else { %>
                                        <small class="text-muted">Unknown prefs</small>
                                    <% } %>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong><%= customer.orders || customer.orderCount || 0 %></strong> orders
                                <% if (customer.lastOrderDate) { %>
                                    <br><small class="text-muted">Last: <%= new Date(customer.lastOrderDate).toLocaleDateString() %></small>
                                <% } else if ((customer.orders || customer.orderCount || 0) === 0) { %>
                                    <br><small class="text-muted">No orders yet</small>
                                <% } %>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong class="text-success">$<%= (customer.totalSpent || 0).toFixed(2) %></strong>
                                <% if (customer.averageOrderValue && customer.averageOrderValue > 0) { %>
                                    <br><small class="text-muted">Avg: $<%= customer.averageOrderValue.toFixed(2) %></small>
                                <% } else if ((customer.orders || customer.orderCount || 0) > 0) { %>
                                    <br><small class="text-muted">Avg: $<%= ((customer.totalSpent || 0) / (customer.orders || customer.orderCount || 1)).toFixed(2) %></small>
                                <% } %>
                            </div>
                        </td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-cog me-1"></i>Actions
                                </button>
                                <ul class="dropdown-menu">
                                    <% if (customer.email) { %>
                                        <li><a class="dropdown-item" href="#" data-action="email-customer" data-customer-email="<%= customer.email %>">
                                            <i class="fas fa-envelope me-2"></i>Send Email
                                        </a></li>
                                    <% } %>
                                    <li><a class="dropdown-item" href="#" data-action="view-orders" data-customer-email="<%= customer.email || customer.phone %>">
                                        <i class="fas fa-shopping-cart me-2"></i>View Orders
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" data-action="create-promotion" data-customer-email="<%= customer.email || customer.phone %>">
                                        <i class="fas fa-tags me-2"></i>Create Promotion
                                    </a></li>
                                    <% if (customer.isPromoCustomer) { %>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#" data-action="convert-to-customer" data-customer-id="<%= customer.id %>">
                                            <i class="fas fa-user-plus me-2"></i>Convert to Customer
                                        </a></li>
                                    <% } %>
                                </ul>
                            </div>
                        </td>
                    </tr>
                <% }) %>
            <% } %>
        </tbody>
    </table>
</div>
</div>
</main>