<%- include('layout', { title: 'Events', currentRoute: '/events' }) %>
<% layout('layout') -%>
<main class="content-area">
  <!-- Enhanced Events Page with Modern UI -->

  <!-- Enhanced CSS Variables and Complete Styling for Events Page -->
  <style>
  :root {
    --events-primary: #007bff;
    --events-primary-dark: #0056b3;
    --events-secondary: #6c757d;
    --events-success: #28a745;
    --events-danger: #dc3545;
    --events-warning: #ffc107;
    --events-info: #17a2b8;
    --events-light: #f8f9fa;
    --events-dark: #343a40;
    --events-border: #dee2e6;
    --events-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --events-shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
    --events-border-radius: 8px;
    --events-border-radius-lg: 16px;
    --events-transition: all 0.3s ease;
  }

  /* Enhanced Filter Chip Styles */
  .filter-chip-group {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
  }

  .filter-label {
    font-weight: 600;
    color: var(--events-dark);
    font-size: 0.875rem;
  }

  .filter-chip {
    background: white;
    border: 2px solid var(--events-border);
    border-radius: 25px;
    padding: 0.375rem 0.875rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--events-secondary);
    transition: var(--events-transition);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    white-space: nowrap;
  }

  .filter-chip:hover {
    border-color: var(--events-primary);
    color: var(--events-primary);
    transform: translateY(-1px);
    box-shadow: var(--events-shadow);
  }

  .filter-chip.active {
    background: linear-gradient(135deg, var(--events-primary) 0%, var(--events-primary-dark) 100%);
    border-color: var(--events-primary);
    color: white;
    box-shadow: var(--events-shadow-lg);
  }

  /* Enhanced Search Input */
  .search-input-group .form-control {
    border-radius: 25px 0 0 25px;
    border-right: none;
    border: 2px solid var(--events-border);
    padding: 0.5rem 1rem;
    transition: var(--events-transition);
  }

  .search-input-group .form-control:focus {
    border-color: var(--events-primary);
    box-shadow: none;
  }

  .search-input-group .btn {
    border-radius: 0 25px 25px 0;
    border: 2px solid var(--events-border);
    border-left: none;
    transition: var(--events-transition);
  }

  .search-input-group .btn:hover {
    background-color: var(--events-primary);
    border-color: var(--events-primary);
    color: white;
  }

  /* Enhanced Tab Navigation */
  .nav-tabs-enhanced {
    border: none;
    padding: 0 1rem;
  }

  .nav-tabs-enhanced .nav-link {
    border: none;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    margin-right: 0.5rem;
    color: var(--events-secondary);
    background: transparent;
    transition: var(--events-transition);
    font-weight: 500;
  }

  .nav-tabs-enhanced .nav-link:hover {
    border: none;
    background: var(--events-light);
    color: var(--events-primary);
    transform: translateY(-2px);
  }

  .nav-tabs-enhanced .nav-link.active {
    border: none;
    background: linear-gradient(135deg, var(--events-primary) 0%, var(--events-primary-dark) 100%);
    color: white;
    box-shadow: var(--events-shadow-lg);
  }

  /* Calendar Grid Styles */
  .calendar-grid-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: var(--events-border);
    border-radius: var(--events-border-radius) var(--events-border-radius) 0 0;
    overflow: hidden;
  }

  .calendar-day-header {
    background: var(--events-primary);
    color: white;
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.875rem;
  }

  .calendar-grid-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: var(--events-border);
    border-radius: 0 0 var(--events-border-radius) var(--events-border-radius);
    overflow: hidden;
    min-height: 400px;
  }

  .calendar-day {
    background: white;
    padding: 0.75rem;
    min-height: 80px;
    position: relative;
    cursor: pointer;
    transition: var(--events-transition);
  }

  .calendar-day:hover {
    background: var(--events-light);
  }

  .calendar-day.other-month {
    background: #fafafa;
    color: var(--events-secondary);
  }

  .calendar-day.today {
    background: linear-gradient(135deg, var(--events-primary) 0%, var(--events-primary-dark) 100%);
    color: white;
  }

  .calendar-day.has-events {
    border-left: 4px solid var(--events-primary);
  }

  .calendar-day-number {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }

  .calendar-events {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .calendar-event {
    background: var(--events-primary);
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 4px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    transition: var(--events-transition);
  }

  .calendar-event:hover {
    transform: scale(1.05);
    z-index: 10;
  }

  /* Event List Styles */
  .event-list-container {
    max-height: 600px;
    overflow-y: auto;
  }

  .event-list-item {
    border: none;
    border-left: 4px solid var(--events-primary);
    transition: var(--events-transition);
    cursor: pointer;
  }

  .event-list-item:hover {
    background: var(--events-light);
    transform: translateX(5px);
  }

  .event-time {
    font-size: 0.875rem;
    color: var(--events-secondary);
    font-weight: 500;
  }

  .event-title {
    font-weight: 600;
    color: var(--events-dark);
    margin-bottom: 0.25rem;
  }

  .event-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
  }

  /* Event Grid Styles */
  .event-card {
    background: white;
    border-radius: var(--events-border-radius);
    padding: 1.5rem;
    box-shadow: var(--events-shadow);
    border: 1px solid var(--events-border);
    transition: var(--events-transition);
    cursor: pointer;
    height: 100%;
  }

  .event-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--events-shadow-lg);
  }

  .event-card-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  .event-card-title {
    font-weight: 600;
    color: var(--events-dark);
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .event-card-date {
    color: var(--events-secondary);
    font-size: 0.875rem;
    font-weight: 500;
  }

  .event-card-description {
    color: var(--events-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .filter-chip-group {
      justify-content: center;
    }
    
    .calendar-grid-header {
      display: none;
    }
    
    .calendar-grid-days {
      grid-template-columns: 1fr;
    }
    
    .calendar-day {
      border-bottom: 1px solid var(--events-border);
      border-radius: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      min-height: 60px;
    }
    
    .search-controls {
      width: 100%;
      margin-top: 1rem;
    }
    
    .nav-tabs-enhanced {
      padding: 0;
    }
    
    .nav-tabs-enhanced .nav-link {
      margin-right: 0.25rem;
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
    }
  }

  .events-page {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    padding: 2rem 0;
  }

  .events-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .page-header {
    background: white;
    border-radius: var(--events-border-radius-lg);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--events-shadow);
    border: 1px solid var(--events-border);
  }

  .page-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--events-dark);
    margin: 0;
  }

  .header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }

  .btn-enhanced {
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 500;
    transition: var(--events-transition);
    border: 2px solid transparent;
  }

  .btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--events-shadow-lg);
  }

  .btn-primary-enhanced {
    background: linear-gradient(135deg, var(--events-primary) 0%, var(--events-primary-dark) 100%);
    border-color: var(--events-primary);
    color: white;
  }

  .btn-outline-enhanced {
    background: transparent;
    border-color: var(--events-primary);
    color: var(--events-primary);
  }

  .btn-outline-enhanced:hover {
    background: var(--events-primary);
    color: white;
  }
  </style>

  <div class="events-page">
    <div class="events-container">
      <!-- Enhanced Page Header -->
      <div class="page-header">
        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
          <div class="header-title-section mb-3 mb-md-0">
            <h1 class="page-title">
              <i class="fas fa-calendar-alt me-3" style="color: var(--events-primary);"></i>
              Events & Design Studio
            </h1>
            <p class="text-muted mb-0 mt-2">Manage events, promotions, and create stunning designs with Canva integration</p>
          </div>
          <div class="header-actions">
            <button id="connectCanvaBtn" class="btn btn-outline-enhanced me-2">
              <i class="fab fa-canva me-2"></i> Connect Canva
            </button>
            <button id="createEventBtn" class="btn btn-primary-enhanced" data-bs-toggle="modal" data-bs-target="#eventModal">
              <i class="fas fa-plus me-2"></i> Create Event
            </button>
          </div>
        </div>
      </div>

      <!-- Enhanced Tabbed Interface -->
      <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white border-0 pb-0">
          <ul class="nav nav-tabs nav-tabs-enhanced" id="eventsTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="events-tab" data-bs-toggle="tab" data-bs-target="#events-content" type="button" role="tab">
                <i class="fas fa-calendar-check me-2"></i>Events Calendar
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="design-studio-tab" data-bs-toggle="tab" data-bs-target="#design-studio-content" type="button" role="tab">
                <i class="fab fa-canva me-2"></i>Design Studio
              </button>
            </li>
          </ul>
        </div>
        <div class="card-body p-0">
          <div class="tab-content" id="eventsTabContent">
            <!-- Events Calendar Tab -->
            <div class="tab-pane fade show active" id="events-content" role="tabpanel">
              <div class="p-4">
                <!-- Enhanced Filter System -->
                <div class="filter-section mb-4">
                  <div class="d-flex flex-column flex-lg-row justify-content-between align-items-start align-items-lg-center mb-3">
                    <div class="filter-controls d-flex flex-wrap gap-2 mb-3 mb-lg-0">
                      <div class="filter-chip-group">
                        <span class="filter-label me-2">Filter by:</span>
                        <button class="filter-chip active" data-filter="all">
                          <i class="fas fa-list me-1"></i>All Events
                        </button>
                        <button class="filter-chip" data-filter="event">
                          <i class="fas fa-calendar me-1"></i>Events
                        </button>
                        <button class="filter-chip" data-filter="promotion">
                          <i class="fas fa-tags me-1"></i>Promotions
                        </button>
                        <button class="filter-chip" data-filter="reminder">
                          <i class="fas fa-bell me-1"></i>Reminders
                        </button>
                        <button class="filter-chip" data-filter="meeting">
                          <i class="fas fa-users me-1"></i>Meetings
                        </button>
                      </div>
                    </div>
                    <div class="search-controls d-flex gap-2">
                      <div class="input-group search-input-group">
                        <input type="text" class="form-control" id="eventSearchInput" placeholder="Search events...">
                        <button class="btn btn-outline-secondary" type="button">
                          <i class="fas fa-search"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- View Options -->
                <div class="view-controls mb-4">
                  <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                    <div class="calendar-nav-controls mb-3 mb-md-0">
                      <div class="btn-group" role="group">
                        <button class="btn btn-outline-secondary" id="prevMonth" title="Previous Month">
                          <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="btn btn-outline-secondary" id="today" title="Today">
                          <i class="fas fa-calendar-day"></i> Today
                        </button>
                        <button class="btn btn-outline-secondary" id="nextMonth" title="Next Month">
                          <i class="fas fa-chevron-right"></i>
                        </button>
                      </div>
                      <h5 id="currentMonthYear" class="ms-3 mb-0 d-inline-block"></h5>
                    </div>
                    <div class="view-options">
                      <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="calendarView" id="monthViewBtn" value="month" autocomplete="off" checked>
                        <label class="btn btn-outline-primary" for="monthViewBtn">
                          <i class="fas fa-calendar me-1"></i>Month
                        </label>
                        <input type="radio" class="btn-check" name="calendarView" id="listViewBtn" value="list" autocomplete="off">
                        <label class="btn btn-outline-primary" for="listViewBtn">
                          <i class="fas fa-list me-1"></i>List
                        </label>
                        <input type="radio" class="btn-check" name="calendarView" id="gridViewBtn" value="grid" autocomplete="off">
                        <label class="btn btn-outline-primary" for="gridViewBtn">
                          <i class="fas fa-th me-1"></i>Grid
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Calendar Display Container -->
                <div id="calendarDisplay" class="calendar-container">
                  <!-- Month View -->
                  <div id="monthView" class="calendar-view">
                    <div class="calendar-grid-header d-none d-md-grid">
                      <div class="calendar-day-header">Sun</div>
                      <div class="calendar-day-header">Mon</div>
                      <div class="calendar-day-header">Tue</div>
                      <div class="calendar-day-header">Wed</div>
                      <div class="calendar-day-header">Thu</div>
                      <div class="calendar-day-header">Fri</div>
                      <div class="calendar-day-header">Sat</div>
                    </div>
                    <div id="calendarGridDays" class="calendar-grid-days">
                      <!-- Calendar days will be generated by JS -->
                    </div>
                  </div>
                  
                  <!-- List View -->
                  <div id="listView" class="calendar-view d-none">
                    <div id="eventListContainer" class="event-list-container">
                      <ul id="eventList" class="list-group list-group-flush">
                        <!-- Event list items will be generated by JS -->
                      </ul>
                    </div>
                  </div>

                  <!-- Grid View -->
                  <div id="gridView" class="calendar-view d-none">
                    <div id="eventGridContainer" class="row">
                      <!-- Event grid items will be generated by JS -->
                    </div>
                  </div>
                </div>
              </div>          </div>

            <!-- Design Studio Tab -->
            <div class="tab-pane fade" id="design-studio-content" role="tabpanel">
              <div class="p-4">

  <!-- Canva Connection Status -->
  <div id="canvaStatus" class="alert alert-info d-none mb-4">
    <div class="d-flex align-items-center">
      <i class="fab fa-canva fa-2x me-3"></i>
      <div>
        <h6 class="mb-1">Canva Integration</h6>
        <p class="mb-0" id="canvaStatusText">Checking connection status...</p>
      </div>
      <button id="disconnectCanvaBtn" class="btn btn-sm btn-outline-danger ms-auto d-none">
        <i class="fas fa-unlink me-1"></i> Disconnect
      </button>
    </div>
  </div>

  <!-- Canva Design Studio Section -->
  <div class="card mb-4" id="canvaStudioCard">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="card-title mb-0">
        <i class="fab fa-canva me-2"></i>
        Canva Design Studio
      </h5>
      <div class="design-actions d-none" id="designActions">
        <button class="btn btn-sm btn-success me-2" id="createDesignBtn">
          <i class="fas fa-plus me-1"></i> New Design
        </button>
        <button class="btn btn-sm btn-outline-secondary" id="refreshDesignsBtn">
          <i class="fas fa-sync-alt me-1"></i> Refresh
        </button>
      </div>
    </div>
    <div class="card-body">
      <div id="canvaDesignsContainer">
        <!-- Not Connected State -->
        <div class="text-center py-5" id="noCanvaConnection">
          <i class="fab fa-canva fa-4x text-muted mb-3"></i>
          <h5 class="text-muted">Connect to Canva</h5>
          <p class="text-muted mb-3">Connect your Canva account to create and manage designs for your events and promotions directly from ShopEasly.</p>
          <button class="btn btn-primary btn-lg" id="connectCanvaMainBtn">
            <i class="fab fa-canva me-2"></i> Connect Canva Account
          </button>
          <div class="mt-3">
            <small class="text-muted">
              <i class="fas fa-shield-alt me-1"></i>
              Secure OAuth connection - we never store your Canva password
            </small>
          </div>
        </div>

        <!-- Loading State -->
        <div class="text-center py-5 d-none" id="designsLoading">
          <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading designs...</span>
          </div>
          <p class="text-muted">Loading your Canva designs...</p>
        </div>

        <!-- Designs Grid -->
        <div id="designsGrid" class="d-none">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">Your Designs</h6>
            <div class="input-group" style="max-width: 300px;">
              <input type="text" class="form-control" id="designSearchInput" placeholder="Search designs...">
              <button class="btn btn-outline-secondary" type="button" id="searchDesignsBtn">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
          <div class="row" id="designsGridContainer">
            <!-- Designs will be populated here -->
          </div>
          <div class="d-flex justify-content-center mt-3">
            <nav aria-label="Designs pagination">
              <ul class="pagination" id="designsPagination">
                <!-- Pagination will be populated here -->
              </ul>
            </nav>
          </div>
        </div>

        <!-- No Designs State -->
        <div class="text-center py-5 d-none" id="noDesigns">
          <i class="fas fa-images fa-4x text-muted mb-3"></i>
          <h5 class="text-muted">No Designs Found</h5>
          <p class="text-muted mb-3">Create your first design in Canva to get started with event promotions.</p>
          <button class="btn btn-primary" id="createFirstDesignBtn">
            <i class="fas fa-plus me-2"></i> Create Your First Design
          </button>
        </div>

        <!-- Error State -->
        <div class="text-center py-5 d-none" id="canvaError">
          <i class="fas fa-exclamation-triangle fa-4x text-warning mb-3"></i>
          <h5 class="text-warning">Connection Error</h5>
          <p class="text-muted mb-3" id="canvaErrorMessage">Unable to connect to Canva. Please try again.</p>
          <button class="btn btn-outline-primary" id="retryCanvaBtn">
            <i class="fas fa-redo me-2"></i> Retry Connection
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="card mb-4">
    <div class="card-body">
      <div id="calendarControls" class="d-flex flex-wrap justify-content-between align-items-center mb-3">
        <div class="btn-group mb-2 mb-md-0" role="group" aria-label="Calendar Navigation">
          <button class="btn btn-outline-secondary" id="prevMonth" title="Previous Month"><i class="fas fa-chevron-left"></i></button>
          <button class="btn btn-outline-secondary" id="today" title="Today"><i class="fas fa-calendar-day"></i></button>
          <button class="btn btn-outline-secondary" id="nextMonth" title="Next Month"><i class="fas fa-chevron-right"></i></button>
        </div>
        <h4 id="currentMonthYear" class="mb-2 mb-md-0 text-center"></h4>
        <div class="btn-group" role="group" aria-label="Calendar View Options">
          <input type="radio" class="btn-check" name="calendarView" id="monthViewBtn" value="month" autocomplete="off" checked>
          <label class="btn btn-outline-primary" for="monthViewBtn">Month</label>
          <input type="radio" class="btn-check" name="calendarView" id="listViewBtn" value="list" autocomplete="off">
          <label class="btn btn-outline-primary" for="listViewBtn">List</label>
        </div>
      </div>
      <div id="calendarDisplay">
        <!-- Month View -->
        <div id="monthView" class="calendar-view">
          <div class="calendar-grid-header d-none d-md-grid">
            <div>Sun</div><div>Mon</div><div>Tue</div><div>Wed</div><div>Thu</div><div>Fri</div><div>Sat</div>
          </div>
          <div id="calendarGridDays" class="calendar-grid-days">
            <!-- Calendar days will be generated by JS -->
          </div>
        </div>
        <!-- List View -->
        <div id="listView" class="calendar-view d-none">
          <ul id="eventList" class="list-group">
            <!-- Event list items will be generated by JS -->
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- Create/Edit Event Modal -->
  <div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <form id="eventForm">
          <input type="hidden" id="eventId" name="id">
          <div class="modal-header">
            <h5 class="modal-title" id="eventModalLabel">Create Event / Promotion</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="eventTitleInput" class="form-label">Title <span class="text-danger">*</span></label>
              <input type="text" id="eventTitleInput" name="title" class="form-control" required>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="eventTypeSelect" class="form-label">Type <span class="text-danger">*</span></label>
                <select id="eventTypeSelect" name="type" class="form-select" required>
                  <option value="" disabled selected>Select type...</option>
                  <option value="event">Event</option>
                  <option value="promotion">Promotion</option>
                  <option value="reminder">Reminder</option>
                  <option value="meeting">Meeting</option>
                </select>
              </div>
              <div class="col-md-6 mb-3">
                <label for="eventColor" class="form-label">Color Tag</label>
                <input type="color" id="eventColor" name="color" class="form-control form-control-color" value="#0d6efd" title="Choose your event color">
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="eventStartInput" class="form-label">Start Date & Time <span class="text-danger">*</span></label>
                <input type="datetime-local" id="eventStartInput" name="start" class="form-control" required>
              </div>
              <div class="col-md-6 mb-3">
                <label for="eventEndInput" class="form-label">End Date & Time <span class="text-danger">*</span></label>
                <input type="datetime-local" id="eventEndInput" name="end" class="form-control" required>
              </div>
            </div>
            <div class="mb-3">
              <label for="eventDescriptionTextarea" class="form-label">Description</label>
              <textarea id="eventDescriptionTextarea" name="description" class="form-control" rows="3"></textarea>
            </div>
            <div class="form-check mb-3">
              <input class="form-check-input" type="checkbox" id="eventAllDay" name="allDay">
              <label class="form-check-label" for="eventAllDay">
                All day event
              </label>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-primary" id="saveEventButton">Save Event</button>
            <button type="button" class="btn btn-danger d-none" id="deleteEventButton">Delete Event</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Event Details Modal -->
  <div class="modal fade" id="eventDetailsModal" tabindex="-1" aria-labelledby="eventDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="eventDetailsModalTitle">Event Details</h5>
          <span id="eventDetailsColorBadge" class="badge ms-2"></span>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p><strong>Type:</strong> <span id="eventDetailsType"></span></p>
          <p><strong>Starts:</strong> <span id="eventDetailsStart"></span></p>
          <p><strong>Ends:</strong> <span id="eventDetailsEnd"></span></p>
          <p id="eventDetailsDescriptionContainer"><strong>Description:</strong> <span id="eventDetailsDescription"></span></p>
        </div>
        <div class="modal-footer justify-content-between">
          <button type="button" class="btn btn-danger" id="eventDetailsDeleteButton">Delete</button>
          <div>
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Close</button>
            <button type="button" class="btn btn-primary" id="eventDetailsEditButton">Edit</button>
          </div>
        </div>
      </div>
    </div>
  </div>


  <div id="message-box-container" class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050">
    <!-- Messages will be appended here by JavaScript -->
  </div>

  <!-- Canva Integration Styles -->
  <style>
  .template-card {
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .template-preview {
    transition: all 0.3s ease;
  }

  .template-card:hover .template-preview {
    transform: scale(1.05);
  }

  .canva-embed-container {
    position: relative;
  }

  .design-info {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    height: 100%;
  }

  @media (max-width: 768px) {
    .embed-responsive {
      height: 250px !important;
    }

    .design-info {
      margin-top: 1rem;
    }
  }
  </style>

  <!-- Canva Integration Scripts -->
  <script src="/js/canva-integration.js"></script>

  <!-- Enhanced Events JavaScript -->
  <script type="module" src="/js/events-enhanced.js"></script>

  <!-- Canva Apps SDK -->
  <script src="https://sdk.canva.com/designbutton/v2/api.js"></script>

  <% /* Ensure events.js is correctly linked via pageScripts in the controller */ %>
</main>