<%- include('layout', { title: 'Dashboard', currentRoute: '/dashboard' }) %>
<% layout('layout') -%>
<main class="content-area">
<!-- Dashboard Content -->
<div class="container-fluid py-3">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-chart-line me-2 text-primary"></i>Dashboard
        </h2>
        <div class="d-flex gap-2">
            <button id="refresh-btn" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
            <div class="dropdown">
                <button id="quick-actions-dropdown" class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-plus me-1"></i>Quick Actions
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/order-management-demo"><i class="fas fa-plus me-2"></i>New Order</a></li>
                    <li><a class="dropdown-item" href="/signup"><i class="fas fa-user-plus me-2"></i>Add Customer</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="/orders"><i class="fas fa-eye me-2"></i>View All Orders</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Quick Stats Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-shopping-bag fa-2x"></i>
                    </div>
                    <h4 class="mb-1" id="totalOrdersCount"><%= totalOrders || 0 %></h4>
                    <small class="text-muted">Total Orders</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h4 class="mb-1" id="pendingOrdersCount">-</h4>
                    <small class="text-muted">Pending Orders</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-truck fa-2x"></i>
                    </div>
                    <h4 class="mb-1" id="shippedOrdersCount">-</h4>
                    <small class="text-muted">Shipped Orders</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                    <h4 class="mb-1">$<span id="totalRevenue"><%= totalRevenue || '0.00' %></span></h4>
                    <small class="text-muted">Total Revenue</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Orders Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Orders
                </h5>
                <div class="d-flex gap-2">
                    <a href="/orders" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>View All Orders
                    </a>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th style="width: 100px;">Order ID</th>
                        <th style="width: 200px;">Customer</th>
                        <th style="width: 180px;" class="d-none d-md-table-cell">Email</th>
                        <th style="width: 100px;">Amount</th>
                        <th style="width: 120px;">Status</th>
                        <th style="width: 140px;" class="d-none d-lg-table-cell">Date</th>
                        <th style="width: 120px;" class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody id="ordersTableBody">
                <% if (!orders || orders.length === 0) { %>
                    <tr>
                        <td colspan="7" class="text-center py-5">
                            <div class="empty-state">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No recent orders</h5>
                                <p class="text-muted mb-0">Recent orders from your shopping site will appear here</p>
                            </div>
                        </td>
                    </tr>
                <% } else { %>
                    <% orders.slice(0, 5).forEach(order => { %>
                        <tr data-order-id="<%= order.id %>" data-status="<%= order.status %>" class="align-middle">
                            <td>
                                <span class="fw-bold text-primary">#<%= order.id %></span>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-semibold"><%= order.customerName || 'N/A' %></div>
                                    <% if (order.customerAddress) { %>
                                        <small class="text-muted"><%= order.customerAddress.substring(0, 30) %>...</small>
                                    <% } %>
                                </div>
                            </td>
                            <td class="d-none d-md-table-cell">
                                <% if (order.customerEmail) { %>
                                    <a href="mailto:<%= order.customerEmail %>" class="text-decoration-none small">
                                        <%= order.customerEmail.length > 25 ? order.customerEmail.substring(0, 25) + '...' : order.customerEmail %>
                                    </a>
                                <% } else { %>
                                    <span class="text-muted small">N/A</span>
                                <% } %>
                            </td>
                            <td>
                                <span class="fw-bold text-success">$<%= parseFloat(order.totalPrice || 0).toFixed(2) %></span>
                            </td>
                            <td>
                                <span class="badge bg-<%= order.status === 'completed' || order.status === 'delivered' ? 'success' : order.status === 'pending' ? 'warning' : order.status === 'cancelled' ? 'danger' : order.status === 'processing' ? 'info' : 'primary' %> text-white">
                                    <%= order.status ? order.status.charAt(0).toUpperCase() + order.status.slice(1) : 'Unknown' %>
                                </span>
                            </td>
                            <td class="d-none d-lg-table-cell">
                                <div class="small">
                                    <div><%= new Date(order.createdAt).toLocaleDateString() %></div>
                                    <div class="text-muted"><%= new Date(order.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %></div>
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="dropdown order-actions">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li>
                                            <a class="dropdown-item" href="#" data-action="update-status" data-order-id="<%= order.id %>">
                                                <i class="fas fa-edit me-2 text-primary"></i>Update Status
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" data-action="shipping-label" data-order-id="<%= order.id %>">
                                                <i class="fas fa-shipping-fast me-2 text-info"></i>Shipping Label
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" data-action="track-order" data-order-id="<%= order.id %>">
                                                <i class="fas fa-map-marker-alt me-2 text-warning"></i>Track Order
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" href="#" data-action="customer-profile" data-order-id="<%= order.id %>">
                                                <i class="fas fa-user me-2 text-success"></i>Customer Profile
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    <% }) %>
                <% } %>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="text-center py-4" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-muted">Loading order data...</p>
    </div>

    <div id="notificationContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1055;"></div>
</div>
</main>
