<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><%= title %> | Shop Easly SAP</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/css/main.css">
</head>

<body>
    <%- include('layout', { title: 'Product Testing', currentRoute: '/product-testing' }) %>
    <% layout('layout') -%>
    <main class="content-area">
      <!-- Only the page content remains. Layout is handled by express-ejs-layouts -->
      <div class="main">
        <div class="page-header d-flex justify-content-between align-items-center mb-4">
          <h1 class="page-title mb-0">Product Simulations</h1>
          <button id="createSimulationBtn" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#simulationModal">
            <i class="fas fa-plus me-1"></i> Create New Simulation
          </button>
        </div>

        <div id="message-box-container" class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050">
          <!-- Messages will be appended here by JavaScript -->
        </div>

        <div class="row" id="simulationsContainer">
          <% if (productSimulations && productSimulations.length) { %>
            <% productSimulations.forEach(sim => { %>
              <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 shadow-sm simulation-card" data-sim-id="<%= sim.id %>">
                  <div class="card-body d-flex flex-column">
                    <h5 class="card-title"><%= sim.name %></h5>
                    <p class="card-text small text-muted mb-2">Scenario: <%= sim.scenario %></p>
                    <div class="mb-2">
                      <span class="badge 
                        <%= sim.status === 'Completed' ? 'bg-success-soft text-success' : '' %>
                        <%= sim.status === 'Pending' ? 'bg-warning-soft text-warning' : '' %>
                        <%= sim.status === 'Running' ? 'bg-info-soft text-info' : '' %>
                        <%= sim.status === 'Failed' ? 'bg-danger-soft text-danger' : '' %>
                        <%= !['Completed', 'Pending', 'Running', 'Failed'].includes(sim.status) ? 'bg-secondary-soft text-secondary' : '' %>">
                        Status: <%= sim.status %>
                      </span>
                    </div>
                    <p class="card-text flex-grow-1"><strong>Result:</strong> <%= sim.result %></p>
                    <div class="mt-auto d-flex justify-content-end gap-2">
                      <button class="btn btn-sm btn-outline-secondary view-details-btn" data-bs-toggle="modal" data-bs-target="#simulationDetailsModal" data-sim-id="<%= sim.id %>">
                        <i class="fas fa-eye me-1"></i> View
                      </button>
                      <button class="btn btn-sm btn-primary run-sim-btn" data-sim-id="<%= sim.id %>">
                        <i class="fas fa-play me-1"></i> Run
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            <% }) %>
          <% } else { %>
            <div class="col-12">
              <div class="card">
                <div class="card-body text-center p-5">
                  <i class="fas fa-flask-vial fa-3x text-muted mb-3"></i>
                  <p class="text-muted">No product simulations available. Create one to get started!</p>
                </div>
              </div>
            </div>
          <% } %>
        </div>

        <!-- Create/Edit Simulation Modal -->
        <div class="modal fade" id="simulationModal" tabindex="-1" aria-labelledby="simulationModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-lg">
            <div class="modal-content">
              <form id="simulationForm">
                <input type="hidden" id="simulationId" name="id">
                <div class="modal-header">
                  <h5 class="modal-title" id="simulationModalLabel">Create New Product Simulation</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <div class="mb-3">
                    <label for="simName" class="form-label">Simulation Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="simName" name="name" required>
                  </div>
                  <div class="mb-3">
                    <label for="simScenario" class="form-label">Scenario <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="simScenario" name="scenario" rows="3" required></textarea>
                  </div>
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="simProduct" class="form-label">Product to Test</label>
                      <select class="form-select" id="simProduct" name="productId">
                        <option value="" selected>Select a product (optional)...</option>
                        <% /* Populate with products from inventory if available */ %>
                        <% if (typeof products !== 'undefined' && products.length > 0) { %>
                          <% products.forEach(product => { %>
                            <option value="<%= product.id %>"><%= product.name %></option>
                          <% }); %>
                        <% } else { %>
                          <option value="" disabled>No products available in inventory</option>
                        <% } %>
                      </select>
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="simStatus" class="form-label">Initial Status</label>
                      <select class="form-select" id="simStatus" name="status">
                        <option value="Pending" selected>Pending</option>
                        <option value="Ready">Ready</option>
                      </select>
                    </div>
                  </div>
                  <div class="mb-3">
                    <label for="simConfig" class="form-label">Configuration (JSON format)</label>
                    <textarea class="form-control" id="simConfig" name="config" rows="4" placeholder='{ "parameter": "value" }'></textarea>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                  <button type="submit" class="btn btn-primary" id="saveSimulationBtn">Save Simulation</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Simulation Details Modal -->
        <div class="modal fade" id="simulationDetailsModal" tabindex="-1" aria-labelledby="simulationDetailsModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-xl">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="simulationDetailsModalTitle">Simulation Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                <h6 id="detailsSimName"></h6>
                <p><small class="text-muted">ID: <span id="detailsSimId"></span></small></p>
                <div class="row">
                  <div class="col-md-6">
                    <p><strong>Status:</strong> <span id="detailsSimStatus" class="badge"></span></p>
                    <p><strong>Scenario:</strong> <span id="detailsSimScenario"></span></p>
                    <p><strong>Product Tested:</strong> <span id="detailsSimProduct"></span></p>
                  </div>
                  <div class="col-md-6">
                    <p><strong>Created:</strong> <span id="detailsSimCreated"></span></p>
                    <p><strong>Last Run:</strong> <span id="detailsSimLastRun"></span></p>
                  </div>
                </div>
                <hr>
                <h6>Configuration:</h6>
                <pre id="detailsSimConfig" class="bg-light p-2 rounded"></pre>
                <hr>
                <h6>Results:</h6>
                <pre id="detailsSimResult" class="bg-light p-2 rounded"></pre>
                <div id="simulationLogsContainer" class="mt-3">
                  <h6>Execution Logs:</h6>
                  <div id="simulationLogs" class="bg-dark text-white p-3 rounded" style="max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.85em;">
                    No logs yet.
                  </div>
                </div>
              </div>
              <div class="modal-footer justify-content-between">
                <div>
                    <button type="button" class="btn btn-danger" id="deleteSimulationBtn">Delete</button>
                </div>
                <div>
                  <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-secondary" id="editSimulationBtn">Edit</button>
                  <button type="button" class="btn btn-primary" id="runSimulationFromDetailsBtn">
                    <i class="fas fa-play me-1"></i> Run Simulation
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <% /* Ensure productTesting.js is correctly linked via pageScripts in the controller */ %>
      </div>
    </main>
</body>
</html>
