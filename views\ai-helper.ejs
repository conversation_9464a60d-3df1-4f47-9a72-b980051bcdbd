<%- include('layout', { title: 'AI Helper', currentRoute: '/ai-helper', excludeSidebar: false }) %>
<% layout('layout') -%>
<main class="content-area">
  <div class="ai-helper-container">
    <header class="header">
      <div class="logo">
        <div class="logo-icon">
          <i class="fas fa-robot"></i>
        </div>
        <span>AI Assistant</span>
      </div>
      <div class="user-menu">
        <div class="model-selector">
          <div class="model-option active">GPT-4</div>
          <div class="model-option">GPT-3.5</div>
        </div>
        <div class="user-avatar">JS</div>
        <button class="settings-btn">
          <i class="fas fa-cog"></i>
        </button>
      </div>
    </header>
    <div class="ai-helper-main-layout" style="display: flex; gap: 2rem; margin-top: 2rem;">
      <aside class="ai-sidebar" style="min-width: 260px; background: #f8f9fa; border-radius: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 1.5rem 1rem;">
        <h3 class="sidebar-title" style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem;">Recent Conversations</h3>
        <ul class="conversation-list" style="list-style: none; padding: 0; margin: 0; display: flex; flex-direction: column; gap: 10px;">
          <li class="conversation-item active" style="background: #e0e7ff; border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer;">
            <div class="conversation-icon" style="background: #6366f1; color: #fff; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
              <i class="fas fa-code"></i>
            </div>
            <div class="conversation-info">
              <div class="conversation-name" style="font-weight: 600;">CSS Optimization Help</div>
              <div class="conversation-preview" style="font-size: 0.95em; color: #555;">How to improve my website's CSS...</div>
            </div>
          </li>
          <li class="conversation-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer;">
            <div class="conversation-icon" style="background: #6366f1; color: #fff; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="conversation-info">
              <div class="conversation-name" style="font-weight: 600;">Data Analysis Project</div>
              <div class="conversation-preview" style="font-size: 0.95em; color: #555;">Can you help me analyze this dataset...</div>
            </div>
          </li>
          <li class="conversation-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer;">
            <div class="conversation-icon" style="background: #6366f1; color: #fff; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
              <i class="fas fa-book"></i>
            </div>
            <div class="conversation-info">
              <div class="conversation-name" style="font-weight: 600;">Book Recommendations</div>
              <div class="conversation-preview" style="font-size: 0.95em; color: #555;">I'm looking for sci-fi novels that...</div>
            </div>
          </li>
        </ul>
        <hr style="margin: 2rem 0; border: none; border-top: 1px solid #e5e7eb;" />
        <h3 class="sidebar-title" style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem;">AI POD Shortcuts</h3>
        <ul class="pod-shortcut-list" style="list-style: none; padding: 0; margin: 0; display: flex; flex-direction: column; gap: 10px;">
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #6366f1; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-box"></i></div>
            <span style="font-size: 1em;">Order Processing</span>
          </li>
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #10b981; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-paint-brush"></i></div>
            <span style="font-size: 1em;">Design Management</span>
          </li>
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #f59e42; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-warehouse"></i></div>
            <span style="font-size: 1em;">Inventory & Supply Chain</span>
          </li>
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #3b82f6; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-headset"></i></div>
            <span style="font-size: 1em;">Customer Service</span>
          </li>
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #ef4444; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-dollar-sign"></i></div>
            <span style="font-size: 1em;">Pricing & Profit</span>
          </li>
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #6366f1; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-industry"></i></div>
            <span style="font-size: 1em;">Production Workflow</span>
          </li>
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #10b981; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-chart-bar"></i></div>
            <span style="font-size: 1em;">Analytics & Reporting</span>
          </li>
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #f59e42; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-bullhorn"></i></div>
            <span style="font-size: 1em;">Marketing & Personalization</span>
          </li>
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #3b82f6; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-search"></i></div>
            <span style="font-size: 1em;">Quality Control</span>
          </li>
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #6366f1; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-plug"></i></div>
            <span style="font-size: 1em;">Platform Integration</span>
          </li>
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #ef4444; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-shield-alt"></i></div>
            <span style="font-size: 1em;">Fraud & Error Detection</span>
          </li>
          <li class="pod-shortcut-item" style="border-radius: 8px; padding: 10px; display: flex; gap: 10px; align-items: center; cursor: pointer; background: #f1f5f9;">
            <div class="pod-shortcut-icon" style="background: #10b981; color: #fff; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 1rem;"><i class="fas fa-leaf"></i></div>
            <span style="font-size: 1em;">Sustainability & Cost</span>
          </li>
        </ul>
      </aside>
      <section class="chat-section" style="flex: 1; background: #fff; border-radius: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 2rem 1.5rem; display: flex; flex-direction: column; min-height: 500px;">
        <div class="chat-history" style="flex: 1; overflow-y: auto; margin-bottom: 1.5rem;">
          <!-- Chat messages will go here -->
        </div>
        <form class="chat-input-form" style="display: flex; gap: 1rem;">
          <input type="text" placeholder="Type your message..." autocomplete="off" style="flex: 1; padding: 0.75rem 1rem; border-radius: 8px; border: 1px solid #e5e7eb; font-size: 1rem;" />
          <button type="submit" style="background: #6366f1; color: #fff; border: none; border-radius: 8px; padding: 0.75rem 1.5rem; font-size: 1.1rem; display: flex; align-items: center; gap: 0.5rem; cursor: pointer;"><i class="fas fa-paper-plane"></i></button>
        </form>
      </section>
    </div>
  </div>
</main>
