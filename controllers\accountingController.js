import path from 'path';
import fs from 'fs/promises';
import { loadOrders } from '../dataLoader.js'; // Ensure this path is correct

// Controller Methods
export const showAccounting = async (req, res) => {
  try {
    // Load accounting data (if any specific accounting.json is still needed)
    // For now, we primarily use orders data for the accounting view as per EJS structure.
    let accountingSpecificData = [];
    try {
      const data = await fs.readFile(path.join(__dirname, '../data/accounting.json'), 'utf-8');
      accountingSpecificData = JSON.parse(data);
    } catch (e) {
      // console.warn('accounting.json not found or unreadable, proceeding without it.');
      accountingSpecificData = []; // Default to empty if not found or error
    }

    const orders = await loadOrders();

    res.render('accounting', {
        title: 'Accounting Overview',
        currentRoute: '/accounting',
        pageStyles: ['/css/sidebar.css'],
        pageScripts: ['/js/accounting.js', '/js/sidebar.js'],
        excludeParticles: true,
        excludeSidebar: false,
        excludeHeader: false,
        excludeFooter: false,
        orders,
        accountingSpecificData,
        user: req.user
    });
  } catch (error) {
    console.error('Error rendering accounting page:', error);
    res.status(500).render('error', { // Render an error page
        pageTitle: 'Error',
        message: 'An error occurred while loading the accounting page.',
        error, // Pass error for debugging if in development
        user: req.user,
        currentRoute: '/accounting'
    });
  }
};
