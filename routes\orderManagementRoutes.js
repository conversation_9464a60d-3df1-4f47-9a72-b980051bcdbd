// File: routes/orderManagementRoutes.js
// Main route for Order Management (formerly ordersRoutes.js)

const express = require('express');
const router = express.Router();
import { showOrderManagementDemo } from '../controllers/orderManagementController.js';

// Canonical Order Management Demo page route
router.get('/', showOrderManagementDemo);

// Sample order data (replace with database or service call in production)
const orders = [
  { id: 1, customer: '<PERSON>', date: '2023-05-15', items: 3, total: 125.99, status: 'Delivered' },
  { id: 2, customer: '<PERSON>', date: '2023-05-16', items: 2, total: 89.50, status: 'Processing' },
  { id: 3, customer: '<PERSON>', date: '2023-05-17', items: 5, total: 210.75, status: 'Shipped' },
  { id: 4, customer: '<PERSON>', date: '2023-05-18', items: 1, total: 75.99, status: 'Pending' }
];

// Render Order Management page
router.get('/', (req, res) => {
  res.render('orders', {
    orders,
    totalOrders: orders.length,
    error: null
  });
});

// Provide order data as JSON
router.get('/api/data', (req, res) => {
  res.json(orders);
});

module.exports = router;
