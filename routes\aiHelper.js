const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');

// AI Helper page route
router.get('/', (req, res) => {
  const aiHelperView = path.join(__dirname, '../views/ai-helper.ejs');
  if (fs.existsSync(aiHelperView)) {
    res.render('ai-helper', {
      title: 'AI Assistant | ShopEasly'
    });
  } else {
    res.render('aiHelper', {
      title: 'AI Assistant | ShopEasly'
    });
  }
});

// This file has been renamed to ai-helper.js for naming consistency. See routes/ai-helper.js.

module.exports = router;
