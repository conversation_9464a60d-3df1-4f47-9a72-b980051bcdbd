import path from 'path';
import fs from 'fs/promises';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Helper function to load events
async function loadEventsData() {
    try {
        const data = await fs.readFile(path.join(__dirname, '../data/events.json'), 'utf-8');
        return JSON.parse(data);
    } catch (e) {
        // If file doesn't exist or is invalid, return empty array or handle error
        console.warn('events.json not found or unreadable, starting with empty events list.');
        return [];
    }
}

// Helper function to save events
async function saveEventsData(events) {
    try {
        await fs.writeFile(path.join(__dirname, '../data/events.json'), JSON.stringify(events, null, 2));
    } catch (e) {
        console.error('Failed to save events.json:', e);
        throw new Error('Failed to save events data.');
    }
}

// Controller Methods
export const showEvents = async (req, res) => {
  try {
    const events = await loadEventsData();
    res.render('events', {
        title: 'Events & Promotions Calendar',
        currentRoute: '/events',
        pageStyles: ['/css/sidebar.css', '/css/events.css'],
        pageScripts: ['/js/events-enhanced.js', '/js/sidebar.js', '/js/canva-integration.js'],
        excludeParticles: true,
        excludeSidebar: false,
        excludeHeader: false,
        excludeFooter: false,
        events,
        user: req.user
    });
  } catch (error) {
    console.error('Error loading events page:', error);
    res.status(500).render('error', {
        pageTitle: 'Error',
        message: 'Failed to load events page.',
        error,
        user: req.user,
        currentRoute: '/events'
    });
  }
};

// API endpoint to get all events
export const getEvents = async (req, res) => {
    try {
        const events = await loadEventsData();
        res.json(events);
    } catch (error) {
        console.error('Error fetching events:', error);
        res.status(500).json({ message: 'Failed to fetch events.', error: error.message });
    }
};

// API endpoint to create a new event
export const createEvent = async (req, res) => {
    try {
        const newEvent = req.body;
        // Basic validation (more robust validation should be added)
        if (!newEvent.title || !newEvent.start || !newEvent.end || !newEvent.type) {
            return res.status(400).json({ message: 'Missing required event fields.' });
        }
        const events = await loadEventsData();
        newEvent.id = newEvent.id || Date.now().toString(); // Simple ID generation
        events.push(newEvent);
        await saveEventsData(events);
        res.status(201).json({ message: 'Event created successfully', event: newEvent });
    } catch (error) {
        console.error('Error creating event:', error);
        res.status(500).json({ message: 'Failed to create event.', error: error.message });
    }
};

// API endpoint to update an event
export const updateEvent = async (req, res) => {
    try {
        const eventId = req.params.id;
        const updatedEventData = req.body;
        const events = await loadEventsData();
        const eventIndex = events.findIndex(event => event.id === eventId);

        if (eventIndex === -1) {
            return res.status(404).json({ message: 'Event not found.' });
        }

        // Merge existing event with new data
        events[eventIndex] = { ...events[eventIndex], ...updatedEventData };
        await saveEventsData(events);
        res.json({ message: 'Event updated successfully', event: events[eventIndex] });
    } catch (error) {
        console.error('Error updating event:', error);
        res.status(500).json({ message: 'Failed to update event.', error: error.message });
    }
};

// API endpoint to delete an event
export const deleteEvent = async (req, res) => {
    try {
        const eventId = req.params.id;
        const events = await loadEventsData();
        const filteredEvents = events.filter(event => event.id !== eventId);

        if (events.length === filteredEvents.length) {
            return res.status(404).json({ message: 'Event not found.' });
        }

        await saveEventsData(filteredEvents);
        res.json({ message: 'Event deleted successfully' });
    } catch (error) {
        console.error('Error deleting event:', error);
        res.status(500).json({ message: 'Failed to delete event.', error: error.message });
    }
};
