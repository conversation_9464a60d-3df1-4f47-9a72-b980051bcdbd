// This file has been replaced by orders.js for canonical Orders JS asset. Please use orders.js.

// Production Order Management JavaScript
// Handles order status updates, customer notifications, and UI interactions

class OrderManager {
  constructor() {
    this.baseUrl = '/api/orders';
    this.currentOrderId = null;
    this.orders = [];
    this.init();
  }

  init() {
    // Wait for Bootstrap to be available
    if (typeof bootstrap === 'undefined') {
      console.log('⏳ Waiting for Bootstrap to load...');
      setTimeout(() => this.init(), 100);
      return;
    }

    console.log('🚀 Initializing Order Manager...');
    this.loadOrders();
    this.bindEvents();
    this.updateStats();
  }

  bindEvents() {
    // Refresh button
    document.getElementById('refreshBtn')?.addEventListener('click', () => {
      this.loadOrders();
    });

    // Export button
    document.getElementById('exportBtn')?.addEventListener('click', () => {
      this.exportOrders();
    });

    // Search functionality
    document.getElementById('searchInput')?.addEventListener('input', (e) => {
      this.filterOrders(e.target.value);
    });

    // Status filter
    document.getElementById('statusFilter')?.addEventListener('change', (e) => {
      this.filterByStatus(e.target.value);
    });

    // Action buttons - Fixed event delegation
    document.addEventListener('click', (e) => {
      // Check if clicked element or its parent has data-action
      const actionElement = e.target.closest('[data-action]');
      if (!actionElement) return;

      e.preventDefault();

      const action = actionElement.dataset.action;
      const orderId = actionElement.dataset.orderId || actionElement.getAttribute('data-order-id');

      console.log(`🔧 Action triggered: ${action}, Order ID: ${orderId}`);

      if (action && orderId) {
        this.handleAction(action, orderId);
      } else {
        console.warn('⚠️ Missing action or order ID:', { action, orderId });
      }
    });

    // Modal form submissions
    document.getElementById('updateStatusBtn')?.addEventListener('click', () => {
      this.updateOrderStatus();
    });

    document.getElementById('createShippingLabel')?.addEventListener('click', () => {
      this.createShippingLabel();
    });

    // Copy tracking number functionality
    document.getElementById('copyTrackingNumber')?.addEventListener('click', () => {
      this.copyTrackingNumber();
    });

    // Refresh tracking functionality
    document.getElementById('refreshTracking')?.addEventListener('click', () => {
      if (this.currentOrderId) {
        const order = this.orders.find(o => o.id == this.currentOrderId);
        if (order) {
          this.loadTrackingDetails(order);
        }
      }
    });

    // View full customer profile functionality
    document.getElementById('viewFullCustomerProfile')?.addEventListener('click', () => {
      if (this.currentOrderId) {
        const order = this.orders.find(o => o.id == this.currentOrderId);
        if (order) {
          this.viewCustomerProfile(order);
        }
      }
    });
  }

  async loadOrders() {
    try {
      this.showLoading(true);
      const response = await fetch(this.baseUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      this.orders = Array.isArray(data) ? data : data.data || [];
      this.renderOrders();
      this.updateStats();
      this.showNotification('Orders loaded successfully', 'success');
    } catch (error) {
      console.error('Error loading orders:', error);
      this.showNotification('Failed to load orders: ' + error.message, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  renderOrders() {
    const tbody = document.getElementById('ordersTableBody');
    if (!tbody) return;

    if (this.orders.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="7" class="text-center py-5">
            <div class="empty-state">
              <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No orders found</h5>
              <p class="text-muted mb-0">Orders from your shopping site will appear here</p>
            </div>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = this.orders.map(order => this.renderOrderRow(order)).join('');

    // Initialize Bootstrap dropdowns after rendering
    this.initializeDropdowns();
  }

  renderOrderRow(order) {
    const statusClass = this.getStatusClass(order.status);
    const formattedDate = new Date(order.createdAt).toLocaleDateString();
    const formattedTime = new Date(order.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

    return `
      <tr data-order-id="${order.id}" data-status="${order.status}" class="align-middle">
        <td>
          <span class="fw-bold text-primary">#${order.id}</span>
        </td>
        <td>
          <div>
            <div class="fw-semibold">${order.customerName || 'N/A'}</div>
            ${order.customerAddress ? `<small class="text-muted">${order.customerAddress.substring(0, 30)}...</small>` : ''}
          </div>
        </td>
        <td class="d-none d-md-table-cell">
          ${order.customerEmail ?
            `<a href="mailto:${order.customerEmail}" class="text-decoration-none small">
              ${order.customerEmail.length > 25 ? order.customerEmail.substring(0, 25) + '...' : order.customerEmail}
            </a>` :
            '<span class="text-muted small">N/A</span>'
          }
        </td>
        <td>
          <span class="fw-bold text-success">$${parseFloat(order.totalPrice || 0).toFixed(2)}</span>
        </td>
        <td>
          <span class="badge bg-${statusClass} text-white">
            ${order.status ? order.status.charAt(0).toUpperCase() + order.status.slice(1) : 'Unknown'}
          </span>
        </td>
        <td class="d-none d-lg-table-cell">
          <div class="small">
            <div>${formattedDate}</div>
            <div class="text-muted">${formattedTime}</div>
          </div>
        </td>
        <td class="text-center">
          <div class="dropdown">
            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
              <li>
                <a class="dropdown-item" href="#" data-action="update-status" data-order-id="${order.id}">
                  <i class="fas fa-edit me-2 text-primary"></i>Update Status
                </a>
              </li>
              <li>
                <a class="dropdown-item" href="#" data-action="shipping-label" data-order-id="${order.id}">
                  <i class="fas fa-shipping-fast me-2 text-info"></i>Shipping Label
                </a>
              </li>
              <li>
                <a class="dropdown-item" href="#" data-action="track-shipment" data-order-id="${order.id}">
                  <i class="fas fa-map-marker-alt me-2 text-warning"></i>Track Order
                </a>
              </li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <a class="dropdown-item" href="#" data-action="customer-profile" data-order-id="${order.id}">
                  <i class="fas fa-user me-2 text-success"></i>Customer Profile
                </a>
              </li>
            </ul>
          </div>
        </td>
      </tr>
    `;
  }

  getStatusClass(status) {
    const statusMap = {
      'completed': 'success',
      'delivered': 'success',
      'pending': 'warning',
      'cancelled': 'danger',
      'processing': 'info',
      'shipped': 'primary'
    };
    return statusMap[status] || 'secondary';
  }

  initializeDropdowns() {
    // Initialize all Bootstrap dropdowns in the table
    const dropdownElements = document.querySelectorAll('#ordersTableBody .dropdown-toggle');

    dropdownElements.forEach(element => {
      try {
        // Dispose of existing dropdown instance if it exists
        const existingDropdown = bootstrap.Dropdown.getInstance(element);
        if (existingDropdown) {
          existingDropdown.dispose();
        }

        // Create new dropdown instance
        new bootstrap.Dropdown(element);
        console.log('✅ Dropdown initialized for element:', element);
      } catch (error) {
        console.error('❌ Error initializing dropdown:', error);
      }
    });

    console.log(`🔧 Initialized ${dropdownElements.length} dropdown menus`);
  }

  handleAction(action, orderId) {
    console.log(`🎯 Handling action: ${action} for order: ${orderId}`);

    const order = this.orders.find(o => o.id == orderId);
    if (!order) {
      console.error(`❌ Order not found: ${orderId}`);
      this.showNotification(`Order #${orderId} not found`, 'error');
      return;
    }

    console.log(`✅ Found order:`, order);

    try {
      switch (action) {
        case 'update-status':
          console.log('📝 Opening status update modal...');
          this.openStatusUpdateModal(order);
          break;
        case 'shipping-label':
          console.log('📦 Opening shipping modal...');
          this.openShippingModal(order);
          break;
        case 'track-shipment':
          console.log('🚚 Opening tracking modal...');
          this.openTrackingModal(order);
          break;
        case 'generate-invoice':
          console.log('📄 Generating invoice...');
          this.generateInvoice(order);
          break;
        case 'customer-profile':
          console.log('👤 Viewing customer profile...');
          this.openCustomerProfileModal(order);
          break;
        default:
          console.error(`❌ Unknown action: ${action}`);
          this.showNotification(`Unknown action: ${action}`, 'error');
      }
    } catch (error) {
      console.error(`❌ Error handling action ${action}:`, error);
      this.showNotification(`Error performing action: ${error.message}`, 'error');
    }
  }

  openStatusUpdateModal(order) {
    try {
      this.currentOrderId = order.id;

      // Populate modal fields
      const statusOrderId = document.getElementById('statusOrderId');
      const currentStatus = document.getElementById('currentStatus');
      const newStatus = document.getElementById('newStatus');
      const statusNotes = document.getElementById('statusNotes');
      const notifyCustomer = document.getElementById('notifyCustomer');

      if (!statusOrderId || !currentStatus || !newStatus) {
        throw new Error('Modal form elements not found');
      }

      statusOrderId.value = `#${order.id}`;
      currentStatus.value = order.status || 'Unknown';
      newStatus.value = '';
      statusNotes.value = '';
      if (notifyCustomer) notifyCustomer.checked = true;

      const modalElement = document.getElementById('statusUpdateModal');
      if (!modalElement) {
        throw new Error('Status update modal not found');
      }

      const modal = new bootstrap.Modal(modalElement);
      modal.show();

      console.log('✅ Status update modal opened for order:', order.id);
    } catch (error) {
      console.error('❌ Error opening status update modal:', error);
      this.showNotification('Error opening status update modal: ' + error.message, 'error');
    }
  }

  async updateOrderStatus() {
    const newStatus = document.getElementById('newStatus').value;
    const notes = document.getElementById('statusNotes').value;
    const notifyCustomer = document.getElementById('notifyCustomer').checked;

    if (!newStatus) {
      this.showNotification('Please select a new status', 'error');
      return;
    }

    try {
      const response = await fetch(`${this.baseUrl}/${this.currentOrderId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          notes: notes,
          notifyCustomer: notifyCustomer
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Update local order data
      const orderIndex = this.orders.findIndex(o => o.id == this.currentOrderId);
      if (orderIndex !== -1) {
        this.orders[orderIndex].status = newStatus;
      }

      // Re-render orders and update stats
      this.renderOrders();
      this.updateStats();

      // Close modal
      const modal = bootstrap.Modal.getInstance(document.getElementById('statusUpdateModal'));
      modal.hide();

      // Show success message
      const customerNotification = notifyCustomer ? ' Customer has been notified.' : '';
      this.showNotification(`Order #${this.currentOrderId} status updated to ${newStatus}.${customerNotification}`, 'success');

    } catch (error) {
      console.error('Error updating order status:', error);
      this.showNotification('Failed to update order status: ' + error.message, 'error');
    }
  }

  openShippingModal(order) {
    try {
      this.currentOrderId = order.id;

      const shippingOrderId = document.getElementById('shippingOrderId');
      const trackingNumber = document.getElementById('trackingNumber');

      if (!shippingOrderId || !trackingNumber) {
        throw new Error('Shipping modal form elements not found');
      }

      shippingOrderId.value = `#${order.id}`;
      trackingNumber.value = '';

      const modalElement = document.getElementById('shippingModal');
      if (!modalElement) {
        throw new Error('Shipping modal not found');
      }

      const modal = new bootstrap.Modal(modalElement);
      modal.show();

      console.log('✅ Shipping modal opened for order:', order.id);
    } catch (error) {
      console.error('❌ Error opening shipping modal:', error);
      this.showNotification('Error opening shipping modal: ' + error.message, 'error');
    }
  }

  createShippingLabel() {
    const carrier = document.getElementById('shippingCarrier').value;
    const trackingNumber = document.getElementById('trackingNumber').value;

    if (!trackingNumber) {
      this.showNotification('Please enter a tracking number', 'error');
      return;
    }

    // Here you would integrate with actual shipping APIs
    // For now, we'll just show a success message
    const modal = bootstrap.Modal.getInstance(document.getElementById('shippingModal'));
    modal.hide();

    this.showNotification(`Shipping label created for order #${this.currentOrderId} with ${carrier}. Tracking: ${trackingNumber}`, 'success');
  }

  openTrackingModal(order) {
    try {
      this.currentOrderId = order.id;

      // Populate basic tracking info
      const modalTrackingNumber = document.getElementById('modalTrackingNumber');
      const modalCarrier = document.getElementById('modalCarrier');
      const modalServiceType = document.getElementById('modalServiceType');

      if (!modalTrackingNumber || !modalCarrier || !modalServiceType) {
        throw new Error('Tracking modal elements not found');
      }

      modalTrackingNumber.textContent = order.trackingNumber || 'Not available';
      modalCarrier.textContent = order.carrier || 'FedEx';
      modalServiceType.textContent = order.serviceType || 'Ground';

      const modalElement = document.getElementById('trackingModal');
      if (!modalElement) {
        throw new Error('Tracking modal not found');
      }

      const modal = new bootstrap.Modal(modalElement);
      modal.show();

      // Load detailed tracking information
      this.loadTrackingDetails(order);

      console.log('✅ Tracking modal opened for order:', order.id);
    } catch (error) {
      console.error('❌ Error opening tracking modal:', error);
      this.showNotification('Error opening tracking modal: ' + error.message, 'error');
    }
  }

  async generateInvoice(order) {
    try {
      this.showNotification('Generating invoice...', 'info');

      const response = await fetch(`${this.baseUrl}/${order.id}/invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get the PDF blob
      const blob = await response.blob();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `invoice-${order.id}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      this.showNotification(`Invoice generated for order #${order.id}`, 'success');
    } catch (error) {
      console.error('Error generating invoice:', error);
      this.showNotification('Failed to generate invoice: ' + error.message, 'error');
    }
  }

  async loadTrackingDetails(order) {
    try {
      const trackingStatus = document.getElementById('trackingStatus');
      const trackingTimeline = document.getElementById('trackingTimeline');

      if (!order.trackingNumber || order.trackingNumber === 'Not available') {
        trackingStatus.innerHTML = `
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            No tracking number available for this order.
          </div>
        `;
        trackingTimeline.innerHTML = `
          <div class="text-center text-muted">
            <i class="fas fa-info-circle me-2"></i>
            Tracking timeline will appear once a tracking number is assigned.
          </div>
        `;
        return;
      }

      const response = await fetch(`${this.baseUrl}/${order.id}/tracking`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const trackingData = await response.json();

      // Update tracking status
      trackingStatus.innerHTML = `
        <div class="d-flex align-items-center">
          <div class="status-indicator bg-${this.getTrackingStatusColor(trackingData.status)} me-3"></div>
          <div>
            <div class="fw-bold">${trackingData.status || 'Unknown'}</div>
            <div class="text-muted small">${trackingData.lastUpdate || 'No updates available'}</div>
          </div>
        </div>
      `;

      // Update tracking timeline
      if (trackingData.events && trackingData.events.length > 0) {
        trackingTimeline.innerHTML = trackingData.events.map(event => `
          <div class="timeline-item d-flex mb-3">
            <div class="timeline-marker bg-primary me-3"></div>
            <div class="timeline-content">
              <div class="fw-semibold">${event.description}</div>
              <div class="text-muted small">${event.location} - ${new Date(event.timestamp).toLocaleString()}</div>
            </div>
          </div>
        `).join('');
      } else {
        trackingTimeline.innerHTML = `
          <div class="text-center text-muted">
            <i class="fas fa-clock me-2"></i>
            No tracking events available yet.
          </div>
        `;
      }

    } catch (error) {
      console.error('Error loading tracking details:', error);
      const trackingStatus = document.getElementById('trackingStatus');
      const trackingTimeline = document.getElementById('trackingTimeline');

      trackingStatus.innerHTML = `
        <div class="alert alert-danger">
          <i class="fas fa-exclamation-circle me-2"></i>
          Failed to load tracking information.
        </div>
      `;

      trackingTimeline.innerHTML = `
        <div class="text-center text-muted">
          <i class="fas fa-exclamation-circle me-2"></i>
          Unable to load tracking timeline.
        </div>
      `;
    }
  }

  getTrackingStatusColor(status) {
    const statusMap = {
      'delivered': 'success',
      'out_for_delivery': 'warning',
      'in_transit': 'info',
      'shipped': 'primary',
      'label_created': 'secondary'
    };
    return statusMap[status?.toLowerCase()] || 'secondary';
  }

  async openCustomerProfileModal(order) {
    try {
      // Populate basic customer info
      document.getElementById('customerName').textContent = order.customerName || 'N/A';
      document.getElementById('customerEmail').textContent = order.customerEmail || 'N/A';
      document.getElementById('customerPhone').textContent = order.customerPhone || 'N/A';
      document.getElementById('customerAddress').textContent = order.customerAddress || 'N/A';

      const modalElement = document.getElementById('customerProfileModal');
      if (!modalElement) {
        throw new Error('Customer profile modal not found');
      }

      const modal = new bootstrap.Modal(modalElement);
      modal.show();

      // Load customer statistics and order history
      this.loadCustomerProfile(order);

      console.log('✅ Customer profile modal opened for order:', order.id);
    } catch (error) {
      console.error('❌ Error opening customer profile modal:', error);
      this.showNotification('Error opening customer profile: ' + error.message, 'error');
    }
  }

  async loadCustomerProfile(order) {
    try {
      const response = await fetch(`/api/customers/profile?email=${encodeURIComponent(order.customerEmail)}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const customerData = await response.json();

      // Update customer statistics
      document.getElementById('customerTotalOrders').textContent = customerData.totalOrders || 0;
      document.getElementById('customerTotalSpent').textContent = `$${(customerData.totalSpent || 0).toFixed(2)}`;
      document.getElementById('customerAvgOrder').textContent = `$${(customerData.averageOrder || 0).toFixed(2)}`;
      document.getElementById('customerSince').textContent = customerData.customerSince ?
        new Date(customerData.customerSince).toLocaleDateString() : 'N/A';

      // Update order history
      const orderHistoryTable = document.getElementById('customerOrderHistory');
      if (customerData.recentOrders && customerData.recentOrders.length > 0) {
        orderHistoryTable.innerHTML = customerData.recentOrders.map(order => `
          <tr>
            <td>#${order.id}</td>
            <td>${new Date(order.createdAt).toLocaleDateString()}</td>
            <td>$${parseFloat(order.totalPrice || 0).toFixed(2)}</td>
            <td><span class="badge bg-${this.getStatusClass(order.status)}">${order.status}</span></td>
          </tr>
        `).join('');
      } else {
        orderHistoryTable.innerHTML = `
          <tr>
            <td colspan="4" class="text-center text-muted">No order history available</td>
          </tr>
        `;
      }

    } catch (error) {
      console.error('Error loading customer profile:', error);
      // Show fallback data
      document.getElementById('customerTotalOrders').textContent = '1';
      document.getElementById('customerTotalSpent').textContent = `$${parseFloat(order.totalPrice || 0).toFixed(2)}`;
      document.getElementById('customerAvgOrder').textContent = `$${parseFloat(order.totalPrice || 0).toFixed(2)}`;
      document.getElementById('customerSince').textContent = new Date(order.createdAt).toLocaleDateString();

      const orderHistoryTable = document.getElementById('customerOrderHistory');
      orderHistoryTable.innerHTML = `
        <tr>
          <td>#${order.id}</td>
          <td>${new Date(order.createdAt).toLocaleDateString()}</td>
          <td>$${parseFloat(order.totalPrice || 0).toFixed(2)}</td>
          <td><span class="badge bg-${this.getStatusClass(order.status)}">${order.status}</span></td>
        </tr>
      `;
    }
  }

  viewCustomerProfile(order) {
    // Redirect to customer profile page
    window.location.href = `/customers?search=${encodeURIComponent(order.customerEmail || order.customerName)}`;
  }

  copyTrackingNumber() {
    const trackingElement = document.getElementById('modalTrackingNumber');
    if (!trackingElement) return;

    const trackingNumber = trackingElement.textContent;
    if (trackingNumber === 'Not available') {
      this.showNotification('No tracking number available to copy', 'warning');
      return;
    }

    // Copy to clipboard
    navigator.clipboard.writeText(trackingNumber).then(() => {
      this.showNotification('Tracking number copied to clipboard', 'success');
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = trackingNumber;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      this.showNotification('Tracking number copied to clipboard', 'success');
    });
  }

  filterOrders(searchTerm) {
    const rows = document.querySelectorAll('#ordersTableBody tr[data-order-id]');
    rows.forEach(row => {
      const text = row.textContent.toLowerCase();
      const visible = text.includes(searchTerm.toLowerCase());
      row.style.display = visible ? '' : 'none';
    });
  }

  filterByStatus(status) {
    const rows = document.querySelectorAll('#ordersTableBody tr[data-order-id]');
    rows.forEach(row => {
      const orderStatus = row.dataset.status;
      const visible = !status || orderStatus === status;
      row.style.display = visible ? '' : 'none';
    });
  }

  updateStats() {
    const totalOrders = this.orders.length;
    const pendingOrders = this.orders.filter(o => o.status === 'pending').length;
    const shippedOrders = this.orders.filter(o => o.status === 'shipped').length;
    const totalRevenue = this.orders.reduce((sum, o) => sum + parseFloat(o.totalPrice || 0), 0);

    document.getElementById('totalOrdersCount').textContent = totalOrders;
    document.getElementById('pendingOrdersCount').textContent = pendingOrders;
    document.getElementById('shippedOrdersCount').textContent = shippedOrders;
    document.getElementById('totalRevenue').textContent = totalRevenue.toFixed(2);
  }

  exportOrders() {
    const csvContent = this.generateCSV();
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `orders-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    this.showNotification('Orders exported successfully', 'success');
  }

  generateCSV() {
    const headers = ['Order ID', 'Customer Name', 'Email', 'Amount', 'Status', 'Date'];
    const rows = this.orders.map(order => [
      order.id,
      order.customerName || '',
      order.customerEmail || '',
      order.totalPrice || 0,
      order.status || '',
      new Date(order.createdAt).toLocaleDateString()
    ]);

    return [headers, ...rows].map(row => row.map(field => `"${field}"`).join(',')).join('\n');
  }

  showLoading(show) {
    const indicator = document.getElementById('loadingIndicator');
    if (indicator) {
      indicator.style.display = show ? 'block' : 'none';
    }
  }

  showNotification(message, type = 'info') {
    if (window.notifications) {
      window.notifications.showToast(message, type, 'Order Production');
    } else {
      // Fallback implementation
      console.log(`📢 ${type.toUpperCase()}: ${message}`);
      const container = document.getElementById('notificationContainer');
      if (!container) {
        alert(`${type.toUpperCase()}: ${message}`);
        return;
      }

      const notification = document.createElement('div');
      notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
      notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      `;

      container.appendChild(notification);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        if (notification.parentElement) {
          notification.remove();
        }
      }, 5000);
    }
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new OrderManager();
});
