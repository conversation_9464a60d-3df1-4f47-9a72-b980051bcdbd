<%- include('layout', { title: 'Inventory', currentRoute: '/inventory' }) %>
<% layout('layout') -%>
<main class="content-area">
  <!-- Header Section -->
  <div class="floating-card mb-4">
      <div class="d-flex justify-content-between align-items-center">
          <div>
              <h2 class="mb-1">
                  <i class="fas fa-boxes me-2 text-primary"></i>Inventory Management
              </h2>
              <p class="text-muted mb-0">Manage materials, finished goods, and packaging supplies</p>
          </div>
          <div class="d-flex gap-2">
              <div class="badge bg-primary-soft text-primary px-3 py-2">
                  <i class="fas fa-cubes me-1"></i>
                  <span id="totalInventoryCount">0</span> Total Items
              </div>
              <div class="badge bg-warning-soft text-warning px-3 py-2">
                  <i class="fas fa-exclamation-triangle me-1"></i>
                  <span id="lowStockCount">0</span> Low Stock
              </div>
          </div>
      </div>
  </div>

  <!-- Message Box -->
  <div id="message-box-container" class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050">
    <!-- Messages will be appended here by JavaScript -->
  </div>

  <!-- Performance Indicators -->
  <div class="rendering-progress" id="renderingProgress"></div>
  <div class="cache-indicator" id="cacheIndicator">Cache Updated</div>


  <!-- Enhanced Inventory Controls -->
  <div class="floating-card mb-4">
      <div class="d-flex flex-wrap justify-content-between align-items-center gap-3">
          <div class="d-flex flex-wrap gap-2">
              <div class="dropdown">
                  <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                      <i class="fas fa-plus me-1"></i> Add Item
                  </button>
                  <ul class="dropdown-menu">
                      <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#inventoryModal" data-category="materials">
                          <i class="fas fa-cogs me-2 text-info"></i>Add Material
                      </a></li>
                      <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#inventoryModal" data-category="products">
                          <i class="fas fa-box me-2 text-success"></i>Add Finished Good
                      </a></li>
                      <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#inventoryModal" data-category="packaging">
                          <i class="fas fa-archive me-2 text-warning"></i>Add Packaging
                      </a></li>
                  </ul>
              </div>
              <button id="upload-inventory-button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#uploadModal">
                  <i class="fas fa-file-excel me-1"></i> Upload Excel
              </button>
              <button id="export-inventory-button" class="btn btn-secondary">
                  <i class="fas fa-download me-1"></i> Export Data
              </button>
              <button id="bulkUpdateBtn" class="btn btn-warning">
                  <i class="fas fa-layer-group me-1"></i> Bulk Update
              </button>
              <button id="syncToReplitButton" class="btn btn-info">Sync to Store</button>
          </div>
          <div class="d-flex flex-wrap gap-2">
              <select id="categoryFilter" class="form-select" style="min-width: 180px;">
                  <option value="">All Categories</option>
                  <option value="materials">Materials</option>
                  <option value="products">Finished Goods</option>
                  <option value="packaging">Packaging</option>
              </select>            <div class="input-group search-input-optimized" style="max-width: 300px; position: relative;">
                  <span class="input-group-text"><i class="fas fa-search"></i></span>
                  <input type="text" id="searchInput" placeholder="Search inventory..." class="form-control" />
                  <div class="search-debounce-indicator" id="searchDebounceIndicator"></div>
              </div>
          </div>
      </div>
  </div>


  <!-- Materials Section -->
  <div class="card mb-4">
    <div class="card-header">
      <div class="d-flex justify-content-between align-items-center">
        <h2 class="card-title h5 mb-0">Materials</h2>
        <div class="text-muted small">
          <span id="materialCount"><%= materials ? materials.length : 0 %></span> items
        </div>
      </div>
    </div>  <div class="card-body p-0">
      <div class="table-responsive table-container" id="materialsContainer">      <table id="materialsTable" class="table table-striped table-hover mb-0 optimized-table">
          <thead class="table-header-fixed">
            <tr>
              <th style="width: 5%;" scope="col">ID</th>
              <th scope="col">Name</th>
              <th style="width: 10%;" scope="col">Stock</th>
              <th style="width: 10%;" scope="col">Unit</th>
              <th style="width: 10%;" scope="col">Price</th>
              <th style="width: 12%;" scope="col">Status</th>
              <th scope="col">Notes</th>
              <th style="width: 5%;" scope="col">Link</th>
              <th style="width: 10%;" class="text-center" scope="col">Actions</th>
            </tr>
          </thead>
          <tbody id="materialsTableBody">
            <% if (materials && materials.length > 0) { %>
              <% materials.forEach(material => { %>
                <tr data-id="<%= material.id %>">
                  <td><%= material.id %></td>
                  <td class="fw-medium"><%= material.name %></td>
                  <td><%= material.stock %></td>
                  <td><%= material.unit %></td>
                  <td>$<%= material.price ? material.price.toFixed(2) : '0.00' %></td>
                  <td>
                    <span class="badge
                      <%= material.status === 'In Stock' ? 'bg-success-soft text-success' : '' %>
                      <%= material.status === 'Low Stock' ? 'bg-warning-soft text-warning' : '' %>
                      <%= material.status === 'Out of Stock' ? 'bg-danger-soft text-danger' : '' %>
                      <%= material.status === 'On Order' ? 'bg-info-soft text-info' : '' %>
                      <%= material.status === 'Discontinued' ? 'bg-secondary-soft text-secondary' : '' %>">
                      <%= material.status %>
                    </span>
                  </td>
                  <td class="small text-muted"><%= material.notes %></td>
                  <td>
                    <% if (material.link) { %>
                      <a href="<%= material.link %>" target="_blank" class="btn btn-sm btn-outline-light text-primary" title="Open Link">
                        <i class="fas fa-external-link-alt"></i>
                      </a>
                    <% } %>
                  </td>
                  <td class="text-center action-cell">
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li><a class="dropdown-item edit-btn" href="#" data-id="<%= material.id %>" data-type="material">
                          <i class="fas fa-edit me-2 text-primary"></i>Edit
                        </a></li>
                        <li><a class="dropdown-item duplicate-btn" href="#" data-id="<%= material.id %>" data-type="material">
                          <i class="fas fa-copy me-2 text-info"></i>Duplicate
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item delete-btn text-danger" href="#" data-id="<%= material.id %>" data-type="material">
                          <i class="fas fa-trash-alt me-2"></i>Delete
                        </a></li>
                      </ul>
                    </div>
                  </td>
                </tr>
              <% }); %>
            <% } else { %>
              <tr>
                <td colspan="9" class="text-center text-muted p-4">No materials found.</td>
              </tr>
            <% } %>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Finished Goods Section -->
  <div class="card mb-4">
    <div class="card-header">
      <div class="d-flex justify-content-between align-items-center">
        <h2 class="card-title h5 mb-0">Finished Goods</h2>
        <div class="text-muted small">
          <span id="productCount"><%= products ? products.length : 0 %></span> items
        </div>
      </div>
    </div>  <div class="card-body p-0">
      <div class="table-responsive table-container" id="productsContainer">
        <table id="productsTable" class="table table-striped table-hover mb-0 optimized-table">
          <thead class="table-header-fixed">
            <tr>
              <th style="width: 5%;" scope="col">ID</th>
              <th scope="col">Name</th>
              <th style="width: 10%;" scope="col">Stock</th>
              <th style="width: 10%;" scope="col">SKU</th>
              <th style="width: 10%;" scope="col">Price</th>
              <th style="width: 12%;" scope="col">Status</th>
              <th scope="col">Description</th>
              <th style="width: 10%;" class="text-center" scope="col">Actions</th>
            </tr>
          </thead>
          <tbody id="productsTableBody">
            <% if (products && products.length > 0) { %>
              <% products.forEach(product => { %>
                <tr data-id="<%= product.id %>">
                  <td><%= product.id %></td>
                  <td class="fw-medium"><%= product.name %></td>
                  <td><%= product.stock %></td>
                  <td><%= product.sku %></td>
                  <td>$<%= product.price ? product.price.toFixed(2) : '0.00' %></td>
                  <td>
                    <span class="badge
                      <%= product.status === 'In Stock' ? 'bg-success-soft text-success' : '' %>
                      <%= product.status === 'Low Stock' ? 'bg-warning-soft text-warning' : '' %>
                      <%= product.status === 'Out of Stock' ? 'bg-danger-soft text-danger' : '' %>
                      <%= product.status === 'On Order' ? 'bg-info-soft text-info' : '' %>
                      <%= product.status === 'Discontinued' ? 'bg-secondary-soft text-secondary' : '' %>">
                      <%= product.status %>
                    </span>
                  </td>
                  <td class="small text-muted"><%= product.description %></td>
                  <td class="text-center action-cell">
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li><a class="dropdown-item edit-btn" href="#" data-id="<%= product.id %>" data-type="product">
                          <i class="fas fa-edit me-2 text-primary"></i>Edit
                        </a></li>
                        <li><a class="dropdown-item duplicate-btn" href="#" data-id="<%= product.id %>" data-type="product">
                          <i class="fas fa-copy me-2 text-info"></i>Duplicate
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item delete-btn text-danger" href="#" data-id="<%= product.id %>" data-type="product">
                          <i class="fas fa-trash-alt me-2"></i>Delete
                        </a></li>
                      </ul>
                    </div>
                  </td>
                </tr>
              <% }); %>
            <% } else { %>
              <tr>
                <td colspan="8" class="text-center text-muted p-4">No finished goods found.</td>
              </tr>
            <% } %>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Raw Materials Section -->
  <div class="card mb-4">
    <div class="card-header">
      <div class="d-flex justify-content-between align-items-center">
        <h2 class="card-title h5 mb-0">Raw Materials</h2>
        <div class="text-muted small">
          <span id="rawMaterialCount"><%= materials ? materials.length : 0 %></span> items
        </div>
      </div>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table id="rawMaterialsTable" class="table table-striped table-hover mb-0">
          <thead>
            <tr>
              <th style="width: 5%;" scope="col">ID</th>
              <th scope="col">Name</th>
              <th style="width: 10%;" scope="col">Quantity</th>
              <th style="width: 10%;" scope="col">Supplier</th>
              <th style="width: 10%;" class="text-center" scope="col">Actions</th>
            </tr>
          </thead>
          <tbody id="rawMaterialsTableBody">
            <% if (materials && materials.length > 0) { %>
              <% materials.forEach(material => { %>
                <tr data-id="<%= material.id %>">
                  <td><%= material.id %></td>
                  <td class="fw-medium"><%= material.name %></td>
                  <td><%= material.quantity %></td>
                  <td><%= material.supplier %></td>
                  <td class="text-center action-cell">
                    <div class="btn-group" role="group" aria-label="Raw Material Actions">
                      <button class="btn btn-sm btn-outline-primary edit-btn" data-id="<%= material.id %>" data-type="material" title="Edit">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary duplicate-btn" data-id="<%= material.id %>" data-type="material" title="Duplicate">
                        <i class="fas fa-copy"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger delete-btn" data-id="<%= material.id %>" data-type="material" title="Delete">
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              <% }); %>
            <% } else { %>
              <tr>
                <td colspan="5" class="text-center text-muted p-4">No raw materials found.</td>
              </tr>
            <% } %>
          </tbody>
        </table>
      </div>
      <% /* Removed redundant "Add New Material" button, covered by main dropdown */ %>
    </div>
  </div>

  <!-- Packaging Materials Section -->
  <div class="card mb-4">
    <div class="card-header">
      <div class="d-flex justify-content-between align-items-center">
        <h2 class="card-title h5 mb-0">Packaging Materials</h2>
        <div class="text-muted small">
          <span id="packagingMaterialCount"><%= packagingMaterials ? packagingMaterials.length : 0 %></span> items
        </div>
      </div>
    </div>  <div class="card-body p-0">
      <div class="table-responsive table-container" id="packagingContainer">
        <table id="packagingMaterialsTable" class="table table-striped table-hover mb-0 optimized-table">
          <thead class="table-header-fixed">
            <tr>
              <th style="width: 5%;" scope="col">ID</th>
              <th scope="col">Name</th>
              <th style="width: 10%;" scope="col">Quantity</th>
              <th style="width: 10%;" scope="col">Supplier</th>
              <th style="width: 10%;" class="text-center" scope="col">Actions</th>
            </tr>
          </thead>
          <tbody id="packagingMaterialsTableBody">
            <% if (packagingMaterials && packagingMaterials.length > 0) { %>
              <% packagingMaterials.forEach(pkgMaterial => { %>
                <tr data-id="<%= pkgMaterial.id %>">
                  <td><%= pkgMaterial.id %></td>
                  <td class="fw-medium"><%= pkgMaterial.name %></td>
                  <td><%= pkgMaterial.quantity %></td>
                  <td><%= pkgMaterial.supplier %></td>
                  <td class="text-center action-cell">
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li><a class="dropdown-item edit-btn" href="#" data-id="<%= pkgMaterial.id %>" data-type="packaging">
                          <i class="fas fa-edit me-2 text-primary"></i>Edit
                        </a></li>
                        <li><a class="dropdown-item duplicate-btn" href="#" data-id="<%= pkgMaterial.id %>" data-type="packaging">
                          <i class="fas fa-copy me-2 text-info"></i>Duplicate
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item delete-btn text-danger" href="#" data-id="<%= pkgMaterial.id %>" data-type="packaging">
                          <i class="fas fa-trash-alt me-2"></i>Delete
                        </a></li>
                      </ul>
                    </div>
                  </td>
                </tr>
              <% }); %>
            <% } else { %>
              <tr>
                <td colspan="5" class="text-center text-muted p-4">No packaging materials found.</td>
              </tr>
            <% } %>
          </tbody>
        </table>
      </div>
      <% /* Removed redundant "Add New Packaging Material" button, covered by main dropdown */ %>
    </div>
  </div>

  <!-- Add/Edit Inventory Modal -->
  <div class="modal fade" id="inventoryModal" tabindex="-1" aria-labelledby="inventoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <form id="inventoryForm">
          <input type="hidden" id="itemId" name="id">
          <input type="hidden" id="itemType" name="type">
          <div class="modal-header">
            <h5 class="modal-title" id="inventoryModalLabel">Add New Inventory Item</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="itemName" class="form-label">Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="itemName" name="name" required>
              </div>
              <div class="col-md-6 mb-3">
                <label for="itemCategory" class="form-label">Category <span class="text-danger">*</span></label>
                <select class="form-select" id="itemCategory" name="category" required>
                  <option value="" disabled selected>Select category...</option>
                  <option value="materials">Material</option>
                  <option value="finished-goods">Finished Good</option>
                  <option value="packaging">Packaging</option>
                </select>
              </div>
            </div>
            <div class="row">
              <div class="col-md-4 mb-3">
                <label for="itemStock" class="form-label">Stock <span class="text-danger">*</span></label>
                <input type="number" class="form-control" id="itemStock" name="stock" required min="0">
              </div>
              <div class="col-md-4 mb-3" id="itemSkuGroup">
                <label for="itemSku" class="form-label">SKU</label>
                <input type="text" class="form-control" id="itemSku" name="sku" placeholder="Stock Keeping Unit">
              </div>
              <div class="col-md-4 mb-3" id="itemUnitGroup">
                <label for="itemUnit" class="form-label">Unit</label>
                <input type="text" class="form-control" id="itemUnit" name="unit" placeholder="e.g., kg, pcs, m">
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="itemStatus" class="form-label">Status <span class="text-danger">*</span></label>
                <select class="form-select" id="itemStatus" name="status" required>
                  <option value="In Stock">In Stock</option>
                  <option value="Low Stock">Low Stock</option>
                  <option value="Out of Stock">Out of Stock</option>
                  <option value="On Order">On Order</option>
                  <option value="Discontinued">Discontinued</option>
                </select>
              </div>
            </div>
            <div class="mb-3">
              <label for="itemNotes" class="form-label">Notes/Description</label>
              <textarea class="form-control" id="itemNotes" name="notes" rows="3"></textarea>
            </div>
            <div class="mb-3" id="materialLinkGroup">
              <label for="itemLink" class="form-label">Supplier/Source Link (URL)</label>
              <input type="url" class="form-control" id="itemLink" name="link" placeholder="https://supplier.com/item-xyz">
            </div>
            <div class="mb-3" id="imagePreviewGroup" style="display: none;">
              <label class="form-label">Image Preview</label>
              <img id="itemImagePreview" src="#" alt="Image Preview" class="img-fluid rounded" style="max-height: 150px; display: none;">
              <p id="imagePreviewError" class="text-danger small" style="display: none;">Could not load image preview.</p>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary" id="saveInventoryButton">Save Item</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Enhanced Upload Inventory Modal -->
  <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="uploadModalLabel">
            <i class="fas fa-file-excel me-2 text-success"></i>Upload Inventory Excel File
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="uploadForm" enctype="multipart/form-data">
            <!-- File Upload Area -->
            <div id="uploadArea" class="text-center p-4 mb-3 border-2 border-dashed rounded" style="border-color: #dee2e6; cursor: pointer; transition: all 0.3s ease;">
              <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
              <h6 class="mb-2">Drag & drop your Excel file here</h6>
              <p class="text-muted small mb-3">or click to select file</p>
              <input type="file" id="fileInput" name="excelFile" accept=".xlsx,.xls" class="d-none">
              <button type="button" id="selectFileButton" class="btn btn-outline-primary">
                <i class="fas fa-folder-open me-2"></i>Select Excel File
              </button>
            </div>

            <!-- File Info Display -->
            <div id="fileInfoSection" class="d-none mb-4">
              <div class="card bg-light">
                <div class="card-body py-3">
                  <div class="d-flex align-items-center">
                    <i class="fas fa-file-excel text-success me-3 fa-2x"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1" id="fileName">No file selected</h6>
                      <small class="text-muted" id="fileSize">0 KB</small>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="removeFileButton">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Upload Progress -->
            <div id="uploadProgress" class="d-none mb-4">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="small fw-medium">Processing Excel file...</span>
                <span class="small text-muted" id="progressText">0%</span>
              </div>
              <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="progressBar"></div>
              </div>
            </div>

            <!-- Upload Results -->
            <div id="uploadResults" class="d-none">
              <div class="row text-center mb-3">
                <div class="col-3">
                  <div class="card border-primary">
                    <div class="card-body py-2">
                      <h5 class="text-primary mb-1" id="processedCount">0</h5>
                      <small class="text-muted">Processed</small>
                    </div>
                  </div>
                </div>
                <div class="col-3">
                  <div class="card border-success">
                    <div class="card-body py-2">
                      <h5 class="text-success mb-1" id="addedCount">0</h5>
                      <small class="text-muted">Added</small>
                    </div>
                  </div>
                </div>
                <div class="col-3">
                  <div class="card border-warning">
                    <div class="card-body py-2">
                      <h5 class="text-warning mb-1" id="duplicatesCount">0</h5>
                      <small class="text-muted">Duplicates</small>
                    </div>
                  </div>
                </div>
                <div class="col-3">
                  <div class="card border-danger">
                    <div class="card-body py-2">
                      <h5 class="text-danger mb-1" id="errorsCount">0</h5>
                      <small class="text-muted">Errors</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Information Panel -->
            <div class="alert alert-info">
              <h6 class="alert-heading">
                <i class="fas fa-info-circle me-2"></i>Smart Upload Features
              </h6>
              <ul class="mb-0 small">
                <li><strong>Auto-categorization:</strong> Items are automatically sorted into Materials, Products, or Packaging</li>
                <li><strong>Duplicate detection:</strong> Prevents overwriting existing items</li>
                <li><strong>Flexible format:</strong> Handles various Excel column layouts and naming conventions</li>
                <li><strong>Data preservation:</strong> Existing inventory data is never overwritten</li>
              </ul>
            </div>

            <!-- Alert Messages -->
            <div id="uploadErrorLog" class="alert alert-danger d-none" role="alert"></div>
            <div id="uploadSuccessLog" class="alert alert-success d-none" role="alert"></div>
            <div id="uploadInfoLog" class="alert alert-info d-none" role="alert"></div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="confirmUploadButton" disabled>
            <i class="fas fa-upload me-2"></i>Upload & Process
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Duplicate Resolution Modal -->
  <div class="modal fade" id="duplicateResolutionModal" tabindex="-1" aria-labelledby="duplicateResolutionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="duplicateResolutionModalLabel">
            <i class="fas fa-exclamation-triangle text-warning me-2"></i>Resolve Duplicate Items
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="alert alert-warning">
            <h6 class="alert-heading">Duplicate items detected!</h6>
            <p class="mb-0">The following items appear to already exist in your inventory. Please choose how to handle each duplicate:</p>
          </div>

          <div id="duplicateItemsList">
            <!-- Duplicate items will be populated here by JavaScript -->
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel Upload</button>
          <button type="button" class="btn btn-warning" id="skipAllDuplicatesButton">
            <i class="fas fa-forward me-2"></i>Skip All Duplicates
          </button>
          <button type="button" class="btn btn-success" id="resolveDuplicatesButton">
            <i class="fas fa-check me-2"></i>Apply Resolutions
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Confirm Delete Modal -->
  <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="confirmDeleteModalLabel">Confirm Deletion</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          Are you sure you want to delete this item? This action cannot be undone.
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-danger" id="confirmDeleteButton">Delete</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Upload Inventory Modal (Placeholder) -->
  <% /* Original script tag for inventory.js is kept, assuming it's correctly linked by the controller */ %>
  <% /* The controller should provide pageScripts: ['/js/inventory.js'] */%>
</main>
