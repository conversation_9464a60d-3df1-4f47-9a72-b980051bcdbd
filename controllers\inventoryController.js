import path from 'path';
import fs from 'fs/promises';
// import xlsx from 'xlsx'; // Removed unused import
import multer from 'multer';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configure multer for file uploads
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: (_req, file, cb) => {
        const allowedTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.ms-excel', // .xls
            'text/csv' // .csv
        ];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only Excel (.xlsx, .xls) and CSV files are allowed.'));
        }
    }
});

const inventoryPath = path.join(__dirname, '../data', 'inventory.json');
const productsPath = path.join(__dirname, '../data', 'products.json'); // Added path for products.json

async function loadInventory() {
    try {
        const data = await fs.readFile(inventoryPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        if (error.code === 'ENOENT') {
            console.warn('Inventory file not found. Creating a new one.');
            await saveInventory([]);
            return [];
        }
        console.error('Error loading inventory:', error.message);
        return [];
    }
}

// Helper function to load products from products.json
async function loadProductsFile() {
    try {
        const data = await fs.readFile(productsPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        if (error.code === 'ENOENT') {
            console.warn('products.json file not found. Returning empty array.');
            return [];
        }
        console.error('Error loading products.json:', error.message);
        // Propagate the error to be handled by the calling function's catch block
        throw new Error(`Failed to load products.json: ${error.message}`);
    }
}

async function saveInventory(inventory) {
    try {
        await fs.writeFile(inventoryPath, JSON.stringify(inventory, null, 2));
    } catch (error) {
        console.error('Error saving inventory:', error.message);
        throw error;
    }
}

export const showInventory = async (req, res) => {
    try {
        const inventory = await loadInventory();
        const materials = inventory.filter(item => item.type === 'material');
        const products = inventory.filter(item => item.type === 'product');
        const packaging = inventory.filter(item => item.type === 'packaging');

        res.render('inventory', {
            title: 'Real-time Inventory Management',
            currentRoute: '/inventory',
            pageStyles: ['/css/sidebar.css', '/css/inventory.css', '/css/inventory-performance.css'],
            pageScripts: [
                '/js/inventory-performance-monitor.js',
                '/js/inventory-performance-optimizer.js',
                '/js/inventory.js',
                '/js/inventory-realtime.js',
                '/js/inventory-performance-test.js',
                '/js/sidebar.js'
            ],
            excludeParticles: true,
            excludeSidebar: false,
            excludeHeader: false,
            excludeFooter: false,
            materials,
            products,
            packagingMaterials: packaging,
            error: null,
            user: req.user
        });
    } catch (error) {
        console.error('Error loading inventory page:', error.message);

        res.render('inventory', {
            title: 'Inventory Management',
            currentRoute: '/inventory',
            pageStyles: ['/css/sidebar.css', '/css/inventory.css', '/css/inventory-performance.css'],
            pageScripts: [
                '/js/inventory-performance-monitor.js',
                '/js/inventory-performance-optimizer.js',
                '/js/inventory.js',
                '/js/inventory-performance-test.js',
                '/js/sidebar.js'
            ],
            excludeParticles: true,
            excludeSidebar: false,
            excludeHeader: false,
            excludeFooter: false,
            materials: [],
            products: [],
            packagingMaterials: [],
            error: 'Failed to load inventory data: ' + (error.message || 'Unknown error'),
            user: req.user
        });
    }
};

export const updateInventory = async (req, res) => {
    try {
        const { id } = req.params;
        const fieldsToUpdate = req.body;
        const inventory = await loadInventory();
        const index = inventory.findIndex(item => item.id.toString() === id);
        if (index === -1) return res.status(404).json({ error: 'Item not found' });
        inventory[index] = { ...inventory[index], ...fieldsToUpdate };
        await saveInventory(inventory);
        res.json({ message: 'Item updated successfully', item: inventory[index] });
    } catch (error) {
        console.error('Error updating inventory:', error.message);
        res.status(500).json({ error: 'Failed to update inventory', details: error.message });
    }
};

export const addMaterial = async (req, res) => {
    try {
        const newItem = req.body;
        newItem.type = 'material';
        const inventory = await loadInventory();
        newItem.id = inventory.length > 0 ? Math.max(...inventory.map(i => parseInt(i.id) || 0)) + 1 : 1;
        inventory.push(newItem);
        await saveInventory(inventory);
        res.status(201).json({ message: 'Material added', item: newItem });
    } catch (error) {
        console.error('Error adding inventory:', error.message);
        res.status(500).json({ error: 'Failed to add material item', details: error.message });
    }
};

export const addProduct = async (req, res) => {
    try {
        const newItem = req.body;
        newItem.type = 'product'; // Explicitly set type for finished goods
        const inventory = await loadInventory();
        // Ensure ID generation is robust, consider UUIDs or database sequences in a real app
        newItem.id = inventory.length > 0 ? Math.max(0, ...inventory.map(i => parseInt(i.id) || 0)) + 1 : 1;
        inventory.push(newItem);
        await saveInventory(inventory);
        res.status(201).json({ message: 'Product added successfully', item: newItem });
    } catch (error) {
        console.error('Error adding product:', error.message);
        res.status(500).json({ error: 'Failed to add product item', details: error.message });
    }
};

export const deleteInventory = async (req, res) => {
    try {
        const { id } = req.params;
        const inventory = await loadInventory();
        const initialLength = inventory.length;
        const updatedInventory = inventory.filter(item => item.id.toString() !== id);
        const deletedCount = initialLength - updatedInventory.length;
        await saveInventory(updatedInventory);
        if (deletedCount === 0) {
            res.status(404).json({ error: 'Item not found' });
        } else {
            res.json({ message: 'Item deleted successfully' });
        }
    } catch (error) {
        console.error('Error deleting inventory:', error.message);
        res.status(500).json({ error: 'Failed to delete inventory item', details: error.message });
    }
};

// Enhanced Excel upload with smart categorization and duplicate handling
export const uploadExcel = async (req, res) => {
    if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded.' });
    }

    try {
        // Import the inventory service dynamically
        const inventoryService = await import('../services/inventoryService.js');
        const service = inventoryService.default;

        // Process the Excel file using the enhanced service
        const results = await service.processExcelFile(req.file.buffer);

        // Return comprehensive results
        res.json({
            success: true,
            message: `Excel file processed successfully. ${results.added} items added, ${results.duplicates} duplicates found.`,
            summary: {
                processed: results.processed,
                added: results.added,
                duplicates: results.duplicates,
                errors: results.errors,
                categorization: results.categorized
            },
            details: {
                addedItems: results.addedItems,
                duplicateItems: results.duplicateItems,
                errorItems: results.errorItems
            }
        });

    } catch (error) {
        console.error('Error processing Excel file:', error.message);
        res.status(500).json({
            success: false,
            error: 'Failed to process Excel file.',
            details: error.message
        });
    }
};

// New endpoint for handling duplicate resolution
export const resolveDuplicates = async (req, res) => {
    try {
        const { resolutions } = req.body;

        if (!resolutions || !Array.isArray(resolutions)) {
            return res.status(400).json({ error: 'Invalid resolution data provided.' });
        }

        // Import the inventory service dynamically
        const inventoryService = await import('../services/inventoryService.js');
        const service = inventoryService.default;

        const results = await service.resolveDuplicates(resolutions);

        res.json({
            success: true,
            message: `Duplicates resolved. ${results.merged} items merged, ${results.deleted} items deleted.`,
            results
        });

    } catch (error) {
        console.error('Error resolving duplicates:', error.message);
        res.status(500).json({
            success: false,
            error: 'Failed to resolve duplicates.',
            details: error.message
        });
    }
};

// API endpoint to get inventory data by type
export const getInventoryDataByTypeApi = async (req, res) => {
    try {
        const { type } = req.params;
        let data;

        if (type === 'products') {
            const baseProducts = await loadProductsFile(); // From products.json
            const detailedInventory = await loadInventory(); // From inventory.json

            // Create a map of detailed inventory items by ID for efficient lookup
            const detailedInventoryMap = new Map();
            detailedInventory.forEach(item => {
                // We only map items of type 'product' from inventory.json that have an ID
                if (item.type === 'product' && item.id) {
                    detailedInventoryMap.set(item.id, item);
                }
            });

            const enrichedProducts = baseProducts.map(baseProduct => {
                const detailedProduct = detailedInventoryMap.get(baseProduct.id);
                if (detailedProduct) {
                    // Return the full product details from inventory.json
                    // Ensure all fields from baseProduct are also present if inventory.json might be missing some
                    // (though typically inventory.json should be more comprehensive for products)
                    return { ...baseProduct, ...detailedProduct };
                }
                // If a product in products.json doesn't have a corresponding detailed entry in inventory.json
                console.warn(`Product with ID '${baseProduct.id}' from products.json not found with type 'product' in inventory.json. Sending base data from products.json.`);
                return baseProduct; // Fallback to base product
            }).filter(product => product !== undefined); // Should not be necessary if baseProduct is always returned

            data = enrichedProducts;

        } else if (type) {
            // For other types (e.g., materials, packaging), load from inventory.json and filter
            const inventory = await loadInventory();
            data = inventory.filter(item => item.type === type);
        } else {
            // If no type is specified, return all data from inventory.json
            const inventory = await loadInventory();
            data = inventory;
        }
        res.json(data);
    } catch (error) {
        console.error(`Error in getInventoryDataByTypeApi for type '${req.params.type}':`, error.message, error.stack);
        res.status(500).json({
            error: `Failed to fetch inventory data for type '${req.params.type}'`,
            details: error.message
        });
    }
};

// New optimized API endpoint for batched inventory loading
export const getAllInventoryOptimized = async (req, res) => {
    try {
        const startTime = Date.now();

        // Single data load operation instead of multiple API calls
        const inventory = await loadInventory();

        // Process and categorize data efficiently
        const categorizedData = {
            materials: [],
            products: [],
            packaging: [],
            metadata: {
                total: inventory.length,
                loadTime: Date.now() - startTime,
                timestamp: new Date().toISOString(),
                cached: false // Will be true when cache is implemented
            }
        };

        // Single pass through inventory for categorization
        for (const item of inventory) {
            if (item.type === 'material') {
                categorizedData.materials.push(item);
            } else if (item.type === 'product') {
                categorizedData.products.push(item);
            } else if (item.type === 'packaging') {
                categorizedData.packaging.push(item);
            }
        }

        // Add counts to metadata
        categorizedData.metadata.counts = {
            materials: categorizedData.materials.length,
            products: categorizedData.products.length,
            packaging: categorizedData.packaging.length
        };

        // Set cache headers for client-side caching
        res.set({
            'Cache-Control': 'public, max-age=30', // 30 second cache
            'ETag': `"${Buffer.from(JSON.stringify(categorizedData.metadata.counts)).toString('base64')}"`,
            'Last-Modified': categorizedData.metadata.timestamp
        });

        res.json({
            success: true,
            data: categorizedData,
            message: 'Inventory loaded successfully'
        });

    } catch (error) {
        console.error('Error in optimized inventory loading:', error.message);
        res.status(500).json({
            success: false,
            error: 'Failed to load inventory data',
            details: error.message,
            timestamp: new Date().toISOString()
        });
    }
};

// Utility exports for use in other modules
export { loadInventory, saveInventory };
export const uploadMiddleware = upload.single('file');

// Default export for backward compatibility
export default {
    showInventory,
    updateInventory,
    addMaterial,
    addProduct,
    deleteInventory,
    uploadExcel,
    resolveDuplicates,
    getInventoryDataByTypeApi,
    loadInventory,
    saveInventory,
    uploadMiddleware,
    getAllInventoryOptimized
};
