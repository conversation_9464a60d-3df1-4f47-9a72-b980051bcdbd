// routes/login.js
import express from 'express';
import { authHelper } from '../utils/authHelper.js';

const router = express.Router();

// Hardcoded admin credentials for prototype (replace with DB in production)
const ADMIN_USER = process.env.ADMIN_USER || 'admin';
const ADMIN_PASS = process.env.ADMIN_PASS || 'admin123';
let ADMIN_HASH, ADMIN_SALT;

// On startup, hash the admin password
(function initAdminHash() {
  const { hash, salt } = authHelper.hashPassword(ADMIN_PASS);
  ADMIN_HASH = hash;
  ADMIN_SALT = salt;
})();

router.get('/login', (req, res) => {
  if (req.session && req.session.user) {
    return res.redirect('/dashboard');
  }
  res.render('login', { title: 'Admin Login', error: null });
});

router.post('/login', (req, res) => {
  const { username, password } = req.body;
  if (
    username === ADMIN_USER &&
    authHelper.verifyPassword(password, ADMIN_HASH, ADMIN_SALT)
  ) {
    req.session.user = { username };
    return res.redirect('/dashboard');
  }
  res.render('login', { title: 'Admin Login', error: 'Invalid credentials' });
});

router.get('/logout', (req, res) => {
  req.session.destroy(() => {
    res.redirect('/login');
  });
});

export default router;
