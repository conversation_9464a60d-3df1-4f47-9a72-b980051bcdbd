// utils/authHelper.js
import crypto from 'crypto';

/**
 * Helper functions for authentication
 */
export const authHelper = {
  /**
   * Hashes a password with a salt
   * @param {String} password - Plain text password
   * @returns {Object} - Hash and salt
   */
  hashPassword: (password) => {
    const salt = crypto.randomBytes(16).toString('hex');
    const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
    return { hash, salt };
  },

  /**
   * Verifies a password against a hash
   * @param {String} password - Plain text password to verify
   * @param {String} hash - Stored hash
   * @param {String} salt - Stored salt
   * @returns {<PERSON>olean} - Whether password is valid
   */
  verifyPassword: (password, hash, salt) => {
    const verifyHash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
    return hash === verifyHash;
  },

  /**
   * Generates a random token
   * @param {Number} length - Length of token
   * @returns {String} - Random token
   */
  generateToken: (length = 32) => {
    return crypto.randomBytes(length).toString('hex');
  },

  /**
   * Checks if user has required permission
   * @param {Object} user - User object
   * @param {String} permission - Required permission
   * @returns {Boolean} - Whether user has permission
   * Note: This is a single-user application, so all permissions are granted
   */
  hasPermission: (user, permission) => {
    // Single-user application - all permissions granted
    return true;
  }
};

/**
 * Express middleware to ensure user is authenticated (session-based)
 */
export function ensureAuthenticated(req, res, next) {
  if (req.session && req.session.user) {
    return next();
  }
  // For API requests, return 401; for browser, redirect to login
  if (req.originalUrl.startsWith('/api/')) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  res.redirect('/login');
}