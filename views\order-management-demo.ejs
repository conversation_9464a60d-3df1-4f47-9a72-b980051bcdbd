<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Management Demo - <%= title %></title>
    <link rel="stylesheet" href="/css/main.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .demo-section h2 {
            color: #333;
            margin-bottom: 1rem;
            border-bottom: 2px solid #007bff;
            padding-bottom: 0.5rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        .btn:hover { opacity: 0.8; }
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 4px;
            position: relative;
        }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .btn-close {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
        }
        .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .orders-table th,
        .orders-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .orders-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .orders-table tbody tr:hover {
            background-color: #f5f5f5;
        }
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-warning { background-color: #fff3cd; color: #856404; }
        .status-info { background-color: #d1ecf1; color: #0c5460; }
        .status-primary { background-color: #d1ecf1; color: #0c5460; }
        .status-success { background-color: #d4edda; color: #155724; }
        .status-danger { background-color: #f8d7da; color: #721c24; }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        .code-example {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            margin-top: 1rem;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <%- include('layout', { title: 'Order Management Demo', currentRoute: '/order-management' }) %>
    <div class="demo-container">
        <h1>Order Management CRUD Demo</h1>
        <p>This page demonstrates how to perform Create, Read, Update, and Delete operations on orders using the OrderManager JavaScript library.</p>

        <!-- Create Order Section -->
        <div class="demo-section">
            <h2>1. Create New Order</h2>
            <form id="createOrderForm">
                <div class="form-group">
                    <label for="customerName">Customer Name *</label>
                    <input type="text" id="customerName" name="customerName" required>
                </div>
                <div class="form-group">
                    <label for="customerEmail">Customer Email *</label>
                    <input type="email" id="customerEmail" name="customerEmail" required>
                </div>
                <div class="form-group">
                    <label for="customerAddress">Customer Address *</label>
                    <textarea id="customerAddress" name="customerAddress" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="totalPrice">Total Price *</label>
                    <input type="number" id="totalPrice" name="totalPrice" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label for="status">Status</label>
                    <select id="status" name="status">
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">Create Order</button>
            </form>

            <div class="code-example">
// JavaScript Example:
const orderData = {
  customerName: 'John Doe',
  customerEmail: '<EMAIL>',
  customerAddress: '123 Main St, City, State 12345',
  totalPrice: 99.99,
  status: 'pending'
};

try {
  const newOrder = await orderManager.createOrder(orderData);
  console.log('Order created:', newOrder);
} catch (error) {
  console.error('Error:', error.message);
}
            </div>
        </div>

        <!-- Read Orders Section -->
        <div class="demo-section">
            <h2>2. Read Orders</h2>
            <div style="margin-bottom: 1rem;">
                <button class="btn btn-info" onclick="loadAllOrders()">Load All Orders</button>
                <button class="btn btn-warning" onclick="loadPendingOrders()">Load Pending Orders</button>
                <button class="btn btn-success" onclick="loadCompletedOrders()">Load Completed Orders</button>
                <select id="orderStatusDropdown" style="margin-left:1rem; min-width:180px;">
                  <option value="">-- Filter by Status --</option>
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="shipped">Shipped</option>
                  <option value="delivered">Delivered</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
            </div>

            <div id="ordersContainer">
                <p>Click a button above to load orders...</p>
            </div>

            <div class="code-example">
// JavaScript Examples:

// Get all orders
const allOrders = await orderManager.getOrders();

// Get orders with filters
const pendingOrders = await orderManager.getOrders({
  status: 'pending',
  page: 1,
  limit: 10
});

// Get specific order
const order = await orderManager.getOrder('123');
            </div>
        </div>

        <!-- Update Order Section -->
        <div class="demo-section">
            <h2>3. Update Order</h2>
            <form id="updateOrderForm">
                <div class="form-group">
                    <label for="updateOrderId">Order ID *</label>
                    <input type="text" id="updateOrderId" name="orderId" placeholder="Enter order ID to update" required>
                </div>
                <div class="form-group">
                    <label for="updateCustomerName">Customer Name</label>
                    <input type="text" id="updateCustomerName" name="customerName">
                </div>
                <div class="form-group">
                    <label for="updateCustomerEmail">Customer Email</label>
                    <input type="email" id="updateCustomerEmail" name="customerEmail">
                </div>
                <div class="form-group">
                    <label for="updateTotalPrice">Total Price</label>
                    <input type="number" id="updateTotalPrice" name="totalPrice" step="0.01" min="0">
                </div>
                <button type="submit" class="btn btn-warning">Update Order</button>
            </form>

            <div class="code-example">
// JavaScript Example:
const updateData = {
  customerName: 'Jane Doe',
  totalPrice: 149.99
};

try {
  const updatedOrder = await orderManager.updateOrder('123', updateData);
  console.log('Order updated:', updatedOrder);
} catch (error) {
  console.error('Error:', error.message);
}
            </div>
        </div>

        <!-- Update Status Section -->
        <div class="demo-section">
            <h2>4. Update Order Status</h2>
            <form id="updateStatusForm">
                <div class="form-group">
                    <label for="statusOrderId">Order ID *</label>
                    <input type="text" id="statusOrderId" name="orderId" placeholder="Enter order ID" required>
                </div>
                <div class="form-group">
                    <label for="newStatus">New Status *</label>
                    <select id="newStatus" name="status" required>
                        <option value="">Select status...</option>
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-info">Update Status</button>
            </form>

            <div class="code-example">
// JavaScript Example:
try {
  const updatedOrder = await orderManager.updateOrderStatus('123', 'shipped');
  console.log('Status updated:', updatedOrder);
} catch (error) {
  console.error('Error:', error.message);
}
            </div>
        </div>

        <!-- Delete Order Section -->
        <div class="demo-section">
            <h2>5. Delete Order</h2>
            <form id="deleteOrderForm">
                <div class="form-group">
                    <label for="deleteOrderId">Order ID *</label>
                    <input type="text" id="deleteOrderId" name="orderId" placeholder="Enter order ID to delete" required>
                </div>
                <button type="submit" class="btn btn-danger">Delete Order</button>
            </form>

            <div class="code-example">
// JavaScript Example:
try {
  await orderManager.deleteOrder('123');
  console.log('Order deleted successfully');
} catch (error) {
  console.error('Error:', error.message);
}
            </div>
        </div>

        <div style="margin-top: 2rem; text-align: center;">
            <a href="/dashboard" style="color: #007bff; text-decoration: none;">← Back to Dashboard</a>
        </div>
    </div>

    <!-- Load the OrderManager library -->
    <script src="/js/orderManager.js"></script>
    <script>
        // Demo JavaScript functionality

        // Create Order Form Handler
        document.getElementById('createOrderForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);

            const orderData = {
                customerName: formData.get('customerName'),
                customerEmail: formData.get('customerEmail'),
                customerAddress: formData.get('customerAddress'),
                totalPrice: parseFloat(formData.get('totalPrice')),
                status: formData.get('status')
            };

            try {
                form.classList.add('loading');
                const newOrder = await orderManager.createOrder(orderData);
                orderManager.showSuccess(`Order #${newOrder.id} created successfully!`);
                form.reset();

                // Refresh orders if they're currently displayed
                const ordersContainer = document.getElementById('ordersContainer');
                if (ordersContainer.querySelector('.orders-table')) {
                    loadAllOrders();
                }
            } catch (error) {
                orderManager.showError(error.message);
            } finally {
                form.classList.remove('loading');
            }
        });

        // Update Order Form Handler
        document.getElementById('updateOrderForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);

            const orderId = formData.get('orderId');
            const updateData = {};

            // Only include fields that have values
            if (formData.get('customerName')) updateData.customerName = formData.get('customerName');
            if (formData.get('customerEmail')) updateData.customerEmail = formData.get('customerEmail');
            if (formData.get('totalPrice')) updateData.totalPrice = parseFloat(formData.get('totalPrice'));

            try {
                form.classList.add('loading');
                const updatedOrder = await orderManager.updateOrder(orderId, updateData);
                orderManager.showSuccess(`Order #${orderId} updated successfully!`);
                form.reset();

                // Refresh orders if they're currently displayed
                const ordersContainer = document.getElementById('ordersContainer');
                if (ordersContainer.querySelector('.orders-table')) {
                    loadAllOrders();
                }
            } catch (error) {
                orderManager.showError(error.message);
            } finally {
                form.classList.remove('loading');
            }
        });

        // Update Status Form Handler
        document.getElementById('updateStatusForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);

            const orderId = formData.get('orderId');
            const status = formData.get('status');

            try {
                form.classList.add('loading');
                const updatedOrder = await orderManager.updateOrderStatus(orderId, status);
                orderManager.showSuccess(`Order #${orderId} status updated to ${status}!`);
                form.reset();

                // Refresh orders if they're currently displayed
                const ordersContainer = document.getElementById('ordersContainer');
                if (ordersContainer.querySelector('.orders-table')) {
                    loadAllOrders();
                }
            } catch (error) {
                orderManager.showError(error.message);
            } finally {
                form.classList.remove('loading');
            }
        });

        // Delete Order Form Handler
        document.getElementById('deleteOrderForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);

            const orderId = formData.get('orderId');

            try {
                form.classList.add('loading');
                await orderManager.deleteOrder(orderId);
                orderManager.showSuccess(`Order #${orderId} deleted successfully!`);
                form.reset();

                // Refresh orders if they're currently displayed
                const ordersContainer = document.getElementById('ordersContainer');
                if (ordersContainer.querySelector('.orders-table')) {
                    loadAllOrders();
                }
            } catch (error) {
                orderManager.showError(error.message);
            } finally {
                form.classList.remove('loading');
            }
        });

        // Load All Orders
        async function loadAllOrders() {
            const container = document.getElementById('ordersContainer');
            container.innerHTML = '<p>Loading orders...</p>';

            try {
                const result = await orderManager.getOrders({ limit: 20 });
                displayOrders(result.data, container);
            } catch (error) {
                container.innerHTML = `<p class="alert alert-danger">Error loading orders: ${error.message}</p>`;
            }
        }

        // Load Pending Orders
        async function loadPendingOrders() {
            const container = document.getElementById('ordersContainer');
            container.innerHTML = '<p>Loading pending orders...</p>';

            try {
                const result = await orderManager.getOrders({ status: 'pending', limit: 20 });
                displayOrders(result.data, container);
            } catch (error) {
                container.innerHTML = `<p class="alert alert-danger">Error loading orders: ${error.message}</p>`;
            }
        }

        // Load Completed Orders
        async function loadCompletedOrders() {
            const container = document.getElementById('ordersContainer');
            container.innerHTML = '<p>Loading completed orders...</p>';

            try {
                const result = await orderManager.getOrders({ status: 'completed', limit: 20 });
                displayOrders(result.data, container);
            } catch (error) {
                container.innerHTML = `<p class="alert alert-danger">Error loading orders: ${error.message}</p>`;
            }
        }

        // Display Orders in Table
        function displayOrders(orders, container) {
            if (!orders || orders.length === 0) {
                container.innerHTML = '<p>No orders found.</p>';
                return;
            }

            const table = document.createElement('table');
            table.className = 'orders-table';

            table.innerHTML = `
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Customer</th>
                        <th>Email</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${orders.map(order => {
                        const formatted = orderManager.formatOrderForDisplay(order);
                        return `
                            <tr>
                                <td>#${order.id}</td>
                                <td>${order.customerName || 'N/A'}</td>
                                <td>${order.customerEmail || 'N/A'}</td>
                                <td>${formatted.formattedPrice}</td>
                                <td><span class="status-badge ${formatted.statusClass}">${formatted.formattedStatus}</span></td>
                                <td>${formatted.formattedDate}</td>
                                <td>
                                    <button class="btn btn-info btn-sm" onclick="quickStatusUpdate('${order.id}')">Update Status</button>
                                    <button class="btn btn-danger btn-sm" onclick="quickDelete('${order.id}')">Delete</button>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            `;

            container.innerHTML = '';
            container.appendChild(table);
        }

        // Quick Status Update and Quick Delete are now handled in public/js/order-management-demo.js
        // ...existing code...

        // Load orders on page load
        document.addEventListener('DOMContentLoaded', () => {
            loadAllOrders();
        });

        // Order status filter handler
        document.getElementById('orderStatusDropdown').addEventListener('change', async (e) => {
            const status = e.target.value;
            const container = document.getElementById('ordersContainer');
            container.innerHTML = '<p>Loading orders...</p>';

            try {
                const result = await orderManager.getOrders({ status, limit: 20 });
                displayOrders(result.data, container);
            } catch (error) {
                container.innerHTML = `<p class="alert alert-danger">Error loading orders: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
